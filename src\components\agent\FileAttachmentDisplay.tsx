import React from 'react';
// import { FileItem } from '@/types';

interface FileAttachmentDisplayProps {
  files: string[];
  className?: string;
}

const FileAttachmentDisplay: React.FC<FileAttachmentDisplayProps> = ({ files, className = '' }) => {
  const getFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase() || '';
    switch(ext) {
      case 'pdf': return '📄';
      case 'doc': case 'docx': return '📝';
      case 'xls': case 'xlsx': return '📊';
      case 'ppt': case 'pptx': return '📋';
      case 'jpg': case 'jpeg': case 'png': case 'gif': case 'bmp': return '🖼️';
      case 'mp4': case 'avi': case 'mov': case 'wmv': return '🎥';
      case 'mp3': case 'wav': case 'flac': return '🎵';
      case 'zip': case 'rar': case '7z': return '🗜️';
      case 'txt': return '📄';
      default: return '📎';
    }
  };

  const getFileTypeColor = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase() || '';
    switch(ext) {
      case 'pdf': return 'from-red-50 to-red-100 border-red-200';
      case 'doc': case 'docx': return 'from-blue-50 to-blue-100 border-blue-200';
      case 'xls': case 'xlsx': return 'from-green-50 to-green-100 border-green-200';
      case 'ppt': case 'pptx': return 'from-orange-50 to-orange-100 border-orange-200';
      case 'jpg': case 'jpeg': case 'png': case 'gif': case 'bmp': return 'from-purple-50 to-purple-100 border-purple-200';
      case 'mp4': case 'avi': case 'mov': case 'wmv': return 'from-pink-50 to-pink-100 border-pink-200';
      case 'mp3': case 'wav': case 'flac': return 'from-yellow-50 to-yellow-100 border-yellow-200';
      case 'zip': case 'rar': case '7z': return 'from-gray-50 to-gray-100 border-gray-200';
      default: return 'from-sky-50 to-sky-100 border-sky-200';
    }
  };

  if (files.length === 0) return null;

  return (
    <div className={`mt-2 ${className}`}>
      <div className="flex items-center gap-1 mb-1">
        {/* <span className="text-xs font-medium text-white">📁 File đính kèm:</span>
        <span className="text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded-full">
          {files.length}
        </span> */}
      </div>
{/* max-h-24 overflow-y-auto custom-scrollbar */}
      <div className="space-y-2 ">
        {files.map((file, index) => (
          <div
            key={index}
            className={`flex items-center gap-1.5 rounded-md bg-gradient-to-r ${getFileTypeColor(file)} border shadow-sm transition-all duration-200 cursor-pointer min-w-0 p-2.5`}
          >
            <div className="flex-shrink-0">
              <span className="text-xs">{getFileIcon(file)}</span>
            </div>

            <div className="flex-1 min-w-0 overflow-hidden">
              <div className="text-xs font-medium text-gray-800 truncate mt-1 ms-1" title={file}>
                {file}
              </div>
              {/* <div className="text-xs text-gray-500 truncate">
                {file.lastModified}
                {file.user && <span className="ml-1">• {file.user}</span>}
              </div> */}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FileAttachmentDisplay;
