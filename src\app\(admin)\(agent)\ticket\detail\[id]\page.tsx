
"use client";
import { useEffect, useRef, useState } from 'react';
import axios from 'axios';
import clsx from 'clsx';
import { useParams } from 'next/navigation';
import { ticketApi } from '@/api/ticketApi';
import { Conversation, FileItem, Ticket, TicketAction, TicketDetail } from '@/types';
import { TicketRequestStatus, TicketStatus } from '@/enums/AgentCode';
import { FaDownload, FaHistory, FaUpload } from 'react-icons/fa';
import {Timeline} from '@/components/ticket-timeline/Timeline';
import { fileApi } from '@/api/fileApi';
import JSZip from 'jszip';
import ChatWidget from '@/components/agent/AgentChatWidget';
import { conversationApi } from '@/api/conversationApi';

const statusClass: Record<string, string> = {
  'Hoàn thành': 'bg-green-100 text-green-800',
  'Đã từ chối': 'bg-red-100 text-red-800',
  '<PERSON>ang xử lý': 'bg-blue-100 text-blue-800',
  "Chờ xử lý": 'bg-yellow-100 text-yellow-800',
  "Đã hủy": 'bg-gray-100 text-gray-800',
};


const statusColors: Record<string, string> = {
  'Open' : 'bg-yellow-500',
  'Closed': 'bg-green-500',
  'Rejected': 'bg-red-500',
  'Cancelled': 'bg-gray-500',
  'InProgress': 'bg-blue-500',
  'New': 'bg-green-500',
};


export default function TicketDetailPage() {
  const { id } = useParams();
  const [data, setData] = useState<TicketDetail | null>(null);
  const [files, setFiles] = useState<FileItem[]>([])
  const [actions, setActions] = useState<boolean>(false);
  const [ticketActions, setTicketActions] = useState<TicketAction[]>([]);
  const [activeTab, setActiveTab] = useState<'info' | 'files' | 'additional'>('info');
  const [activeStepTab, setActiveStepTab] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const getOverallStatus = (ticketDetail: TicketDetail): string => {
    if(!ticketDetail.overallStatus)
      return "Chờ xử lý"
    return ticketDetail.overallStatus
  }

  const handleAction = async (action: TicketAction) => {
    try {
      const updateTicketStatus = await ticketApi.updateTicketStatus(Number(id), action.stepId, action.statusId)
      if(updateTicketStatus)
      {
        fetchData();
      }
    } catch (err) {
      console.log(err)
    }
  }
  

  const getTicketActions = async () => {
    try {
      const ticketActions = await ticketApi.getTicketAction(Number(id))
      // if(actions == null)
      // {
      //   setActions(false)
      // }
      setTicketActions(ticketActions)
    } catch (err) {
      console.log(err)
    }
  }

  const renderActionLabel = (action: string) => {
    switch (action) {
      case 'INPROGRESS':
        return 'Tiếp nhận'
      case 'REJECTED':
        return 'Từ chối'
      case 'COMPLETED':
        return 'Hoàn thành'
      case 'CANCELLED':
        return 'Hủy'
      default:
        return action
    }
  }

  const getStatusColorClass = (statusCode: string) => {
    switch (statusCode) {
      case 'INPROGRESS':
        return 'bg-[#3752D8] text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors'
      case 'COMPLETED':
        return 'bg-green-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors'
      case 'REJECTED':
        return 'bg-red-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-red-700 transition-colors'
      case 'CANCELLED':
        return 'bg-gray-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors'
      default:
        return 'bg-[#3752D8] text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors'
    }
  }

  const fetchData = async () => {
    try {
      const ticket = await ticketApi.getTicketDetail(Number(id))
      setData(ticket)
      
      if(ticket != null){
        await getTicketActions();
      }
    } catch (err) {
      console.log(err)
    }
  }
  

  const fetchFiles = async () => {
    try {
      const fileList = await ticketApi.getTicketFiles(Number(id))
      setFiles(fileList)
    } catch (err) {
      setFiles([])
      console.log(err)
    }
  }

  useEffect(() => {
    if (id) {
      fetchData();
      fetchFiles();
    }
  }, [id]);

  const downloadFile = async (ticketId: number ,file: FileItem) => {
    if (file.id)
    {
      const downloadLink = await fileApi.getTicketSignedUrl(ticketId, file.id)
      window.open(downloadLink,  "_blank")
    }
  }
  const uploadFile = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if(data){
      const selectedFiles = e.target.files;
      if (selectedFiles) {
          var res = await ticketApi.uploadFile(data.id, [...selectedFiles])
          if(res)
          {
            fetchFiles()
          }
      }
      if (fileInputRef.current) {
          fileInputRef.current.value = "";
      }
    }
  }

  const groupFilesByFolder = (files: FileItem[]) => {
    const grouped: Record<string, any> = {};

    files.forEach((file) => {
      const parts = file.fileName.split('/');
      const nameOnly = parts.pop() || '';
      const folderPath = parts.join('/') || 'root';

      if (!grouped[folderPath]) {
        grouped[folderPath] = [];
      }

      grouped[folderPath].push({ ...file, nameOnly: nameOnly });
    });

    return grouped;
  }

  const downloadAll = async (ticketId: number) => {
    if (files.length)
    {
      setLoading(true)
      const zip = new JSZip();
      for (const file of files) {
        if(file.id == null) continue;

        const downloadLink = await fileApi.getTicketSignedUrl(ticketId, file.id)
        if(!downloadLink) continue;

        const res = await fetch(downloadLink);
        const blob = await res.blob();

        zip.file(file.fileName, blob);
      }

      const content = await zip.generateAsync({ type: 'blob' });
      const a = document.createElement('a');
      a.href = URL.createObjectURL(content);
      a.download =  data?.title +'.zip';
      a.click();
      URL.revokeObjectURL(a.href);

      setLoading(false)
    }
  }

  if (!data) return <div className="p-4 dark:text-white">Đang tải dữ liệu...</div>;

  return (
    <div className="min-h-screen">
      <div className="mx-auto">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6 dark:text-gray-400">
          <span>Trang chủ</span>
          <span>/</span>
          <span>Tickets</span>
          <span>/</span>
          <span className="text-[#3752D8] dark:text-gray-200 font-medium">Chi tiết</span>
        </nav>

        {/* Chat Widget */}
        <ChatWidget ticketId={data.id}/>

        {/* Clean Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 dark:bg-gray-700 dark:border-gray-600">
          <div className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900 mb-3 dark:text-gray-100">{data.title}</h1>
                <div className="flex items-center gap-4">
                  <span
                    className={clsx(
                      'px-3 py-1 rounded-md text-sm font-medium',
                      statusClass[getOverallStatus(data)] || 'bg-gray-100 text-gray-800'
                    )}
                  >
                    {getOverallStatus(data)}
                  </span>
                  <span className="text-gray-500 text-sm dark:text-gray-400">
                    ID: #{data.id}
                  </span>
                </div>
              </div>
              {
                ticketActions.length > 0 && (
                <div className="flex gap-2 flex-wrap">
                  {ticketActions.map((action) => (
                    <button
                      key={action.statusCode}
                      className={`${getStatusColorClass(action.statusCode)}`}
                      onClick={() => handleAction(action)}
                    >
                      {renderActionLabel(action.statusCode)}
                    </button>
                  ))}
                </div>) 
              }
              
            </div>
          </div>
        </div>
        {/* Main Content - Better Column Layout */}
        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6 '>
          {/* Left Column - Main Content (2/3) */}
          <div className='lg:col-span-2 space-y-6 '>
            {/* Tab Navigation */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-gray-700 dark:border-gray-600">
              <div className="border-b border-gray-200 dark:border-gray-600">
                <nav className="flex space-x-8 px-6" aria-label="Tabs">
                  <button
                    onClick={() => setActiveTab('info')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'info'
                        ? 'border-[#3752D8] text-[#3752D8] dark:text-blue-300 dark:border-blue-300'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-300 dark:hover:text-white'
                    }`}
                  >
                    Thông tin yêu cầu
                  </button>
                  <button
                    onClick={() => setActiveTab('files')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'files'
                        ? 'border-[#3752D8] text-[#3752D8] dark:text-blue-300 dark:border-blue-300'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-300 dark:hover:text-white'
                    }`}
                  >
                    Tài liệu đính kèm
                  </button>
                  {data.ticketStatuses && data.ticketStatuses.some(status => status.additionInfos && status.additionInfos.length > 0) && (
                    <button
                      onClick={() => {
                        setActiveTab('additional');
                        setActiveStepTab(0); // Reset về step đầu tiên
                      }}
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'additional'
                          ? 'border-[#3752D8] text-[#3752D8] dark:text-blue-300 dark:border-blue-300'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-300 dark:hover:text-white'
                      }`}
                    >
                      Thông tin bổ sung
                    </button>
                  )}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                {/* Thông tin yêu cầu Tab */}
                {activeTab === 'info' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="text-sm font-medium text-gray-500 mb-1 block dark:text-gray-400">Nội dung</label>
                      <p className="text-gray-900 dark:text-gray-100">{data.description}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500 mb-1 block dark:text-gray-400">Ngày tạo</label>
                      <p className="text-gray-900 dark:text-gray-100">{new Date(data.createdAt).toLocaleString()}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500 mb-1 block dark:text-gray-400">Tool tiếp nhận</label>
                      <p className="text-gray-900 dark:text-gray-100">{data.moduleName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500 mb-1 block dark:text-gray-400">Người yêu cầu</label>
                      <p className="text-gray-900 dark:text-gray-100">{data.createdBy}</p>
                    </div>
                  </div>
                )}

                {/* Tài liệu đính kèm Tab */}
                {activeTab === 'files' && (
                  <div>
                    <input
                        ref={fileInputRef}
                        type="file"
                        className="hidden"
                        onChange={uploadFile}
                        multiple
                    />
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Tài liệu đính kèm</h3>
                      <div className='flex gap-3'>
                        <button className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors dark:bg-gray-700 dark:text-gray-100" onClick={()=>{fileInputRef.current?.click()}}>
                          <span className="text-sm flex items-center gap-2 ">
                            <FaUpload className='text-sm '/> Tải lên
                          </span>
                        </button>
                        <button
                          className="px-4 py-2 rounded-lg bg-[#3752D8] text-white hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => downloadAll(data.id)}
                          disabled={loading}
                        >
                          <span className="text-sm flex items-center gap-2">
                            {loading ? (
                              <>
                                <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Đang tải...
                              </>
                            ) : (
                              <>
                                <FaDownload className='text-sm' />
                                Tải xuống tất cả
                              </>
                            )}
                          </span>
                        </button>
                      </div>
                    </div>

                    <div className="overflow-x-auto ">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="bg-gray-50 border-b border-gray-200 dark:bg-gray-800 dark:border-gray-600">
                            <th className="px-6 py-3 text-left font-medium text-gray-700 dark:text-gray-50">STT</th>
                            <th className="px-6 py-3 text-left font-medium text-gray-700 dark:text-gray-50">Tài liệu</th>
                            <th className="px-6 py-3 text-left font-medium text-gray-700 dark:text-gray-50">Thời gian</th>
                            <th className="px-6 py-3 text-left font-medium text-gray-700 dark:text-gray-50">Người nộp</th>
                            <th className="px-6 py-3 text-center font-medium text-gray-700 dark:text-gray-50">Hành động</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 dark:bg-gray-700 dark:border-gray-600">
                          {Object.entries(groupFilesByFolder(files)).map(([folder, filesInFolder]) => (
                            <>
                              <tr key={folder} className="bg-blue-50 dark:bg-gray-800 dark:border-gray-600">
                                <td className="px-6 py-3 font-medium text-[#3752D8] dark:text-sky-100" colSpan={5}>
                                  <div className="flex items-center gap-2">
                                    <span>📁</span>
                                    <span>{folder === 'root' ? 'Tệp gốc' : folder}</span>
                                  </div>
                                </td>
                              </tr>
                              {filesInFolder.map((value: any, index: any) => (
                                <tr key={folder + index} className="hover:bg-gray-50 transition-colors dark:bg-gray-700 dark:border-gray-600">
                                  <td className="px-6 py-4 text-gray-600 dark:text-gray-100">{index + 1}</td>
                                  <td className="px-6 py-4">
                                    <div className="flex items-center gap-2">
                                      <span className="text-gray-400">📄</span>
                                      <span className="text-gray-900 dark:text-gray-100">{value.nameOnly}</span>
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 text-gray-600 dark:text-gray-100">{value.lastModified ? new Date(value.lastModified).toLocaleString() : "Không rõ"}</td>
                                  <td className="px-6 py-4 text-gray-600 dark:text-gray-100">{value.user}</td>
                                  <td className="px-6 py-4 text-center">
                                    <button
                                      onClick={() => downloadFile(data.id, value)}
                                      className="p-2 rounded-lg bg-[#3752D8] text-white hover:bg-blue-700 transition-colors dark:bg-blue-900"
                                    >
                                      <FaDownload fontSize={14} />
                                    </button>
                                  </td>
                                </tr>
                              ))}
                            </>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* Thông tin bổ sung Tab */}
                {activeTab === 'additional' && (() => {
                  // Lọc ra các step có thông tin bổ sung
                  const stepsWithAdditionalInfo = data.ticketStatuses.filter(
                    status => status.additionInfos && status.additionInfos.length > 0
                  );

                  if (stepsWithAdditionalInfo.length === 0) {
                    return (
                      <div className="text-center py-8 text-gray-500">
                        Không có thông tin bổ sung nào
                      </div>
                    );
                  }

                  return (
                    <div>
                      {/* Sub-tab navigation cho các step */}
                      <div className="border-b border-gray-200 dark:border-gray-600 mb-6">
                        <nav className="flex space-x-6" aria-label="Step tabs">
                          {stepsWithAdditionalInfo.map((status, index) => (
                            <button
                              key={index}
                              onClick={() => setActiveStepTab(index)}
                              className={`py-3 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                                activeStepTab === index
                                  ? 'border-[#3752D8] text-[#3752D8] dark:text-blue-300 dark:border-blue-300'
                                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 hover:dark:text-blue-400 hover:border-gray-300 hover:dark:border-blue-400'
                              }`}
                            >
                              <div className="flex items-center gap-2">
                                <div className={`w-2 h-2 rounded-full ${
                                  status.status ? statusColors[status.status] : 'bg-gray-500'
                                }`} />
                                <span>{status.name}</span>
                              </div>
                            </button>
                          ))}
                        </nav>
                      </div>

                      {/* Nội dung của step được chọn */}
                      {stepsWithAdditionalInfo[activeStepTab] && (
                        <div>
                          <div className="mb-6">
                            <div className="flex items-center gap-3 mb-2">
                              <div className={`w-3 h-3 rounded-full ${
                                stepsWithAdditionalInfo[activeStepTab].status ? statusColors[stepsWithAdditionalInfo[activeStepTab].status] : 'bg-gray-500'
                              }`} />
                              <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-300">
                                {stepsWithAdditionalInfo[activeStepTab].name}
                              </h4>
                            </div>
                            <p className="text-sm text-gray-500 ml-6">
                              Cập nhật: {stepsWithAdditionalInfo[activeStepTab].updatedAt
                                ? new Date(stepsWithAdditionalInfo[activeStepTab].updatedAt).toLocaleString()
                                : new Date(stepsWithAdditionalInfo[activeStepTab].createdAt).toLocaleString()}
                            </p>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {stepsWithAdditionalInfo[activeStepTab].additionInfos?.map((info, infoIndex) => (
                              <div key={infoIndex} className="bg-gray-50 p-4 rounded-lg border border-gray-200 dark:bg-gray-800 dark:border-gray-600">
                                <label className="text-sm font-medium text-gray-700 mb-2 block dark:text-gray-300">{info.key}</label>
                                <div className="text-sm text-gray-900 bg-white px-3 py-2 rounded border min-h-[40px] flex items-center dark:bg-gray-900 dark:text-gray-200 dark:border-gray-700">
                                  {info.type === 'file' ? (
                                    <span className="text-[#3752D8] underline cursor-pointer hover:text-blue-700 transition-colors">
                                      {info.value}
                                    </span>
                                  ) : (
                                    <span className=''>{info.value}</span>
                                  )}
                                </div>
                                <span className="text-xs text-gray-400 mt-1 block">Loại: {info.type}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })()}
              </div>
            </div>
          </div>

          {/* Right Column - Timeline (1/3) */}
          <div className='lg:col-span-1'>
            <div className='bg-white rounded-lg shadow-sm border border-gray-200 sticky top-6 dark:bg-gray-700 dark:border-gray-600'>
              <div className="px-6 py-4 border-b border-gray-100 dark:border-gray-600">
                <div className="flex items-center gap-2">
                  <FaHistory className='text-[#3752D8] h-5 w-5 dark:text-blue-400'/>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Lịch sử xử lý</h3>
                </div>
              </div>
              <div className="p-6">
                {data.ticketStatuses && <Timeline timelineData={data.ticketStatuses}/>}
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
}
