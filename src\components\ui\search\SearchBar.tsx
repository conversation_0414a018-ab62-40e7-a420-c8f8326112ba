'use client';

import React from 'react';
import { FaSearch } from 'react-icons/fa';

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSearch?: () => void;
  placeholder?: string;
  className?: string;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  onSearch,
  placeholder = "Tìm kiếm...",
  className = "",
  onKeyDown
}) => {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && onSearch) {
      onSearch();
    }
    if (onKeyDown) {
      onKeyDown(e);
    }
  };

  return (
    <div className={`gradient-search-bar ${className}`}>
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className="gradient-search-input h-10 bg-white dark:bg-gray-900 dark:text-white border-[#e5e7eb] border-2 dark:border-gray-700"
      />
      <FaSearch className="gradient-search-icon" />
    </div>
  );
};

export default SearchBar;
