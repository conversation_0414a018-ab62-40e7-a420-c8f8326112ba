"use client";
import Image from "next/image";
import React, { useState } from "react";
import { Dropdown } from "../ui/dropdown/Dropdown";
import { DropdownItem } from "../ui/dropdown/DropdownItem";
import { ChatBubbleLeftEllipsisIcon, XMarkIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

export default function MessageDropdown() {
  const [isOpen, setIsOpen] = useState(false);
  const [hasNewMessage, setHasNewMessage] = useState(true);

  function toggleDropdown() {
    setIsOpen(!isOpen);
  }

  function closeDropdown() {
    setIsOpen(false);
  }

  const handleClick = () => {
    toggleDropdown();
    setHasNewMessage(false);
  };

  return (
    <div className="relative">
      <button
  className="relative dropdown-toggle flex items-center justify-center text-gray-500 transition-colors bg-white border border-gray-200 rounded-full hover:text-gray-700 h-11 w-11 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white"
  onClick={handleClick}
>
  <span
    className={`absolute right-0 top-0.5 z-10 h-2 w-2 rounded-full ${
      !hasNewMessage ? "hidden" : "flex"
    }`}
    style={{ backgroundColor: '#FD853A' }}
  >
    <span
      className="absolute inline-flex w-full h-full rounded-full opacity-75 animate-ping"
      style={{ backgroundColor: '#FD853A' }}
    ></span>
  </span>
  <ChatBubbleLeftEllipsisIcon className="w-5 h-5" />
</button>
      <Dropdown
  isOpen={isOpen}
  onClose={closeDropdown}
  className="absolute -right-[240px] mt-[17px] flex h-[480px] w-[350px] flex-col rounded-2xl border border-gray-200 bg-white p-3 shadow-theme-lg dark:border-gray-800 dark:bg-gray-dark sm:w-[361px] lg:right-0"
>
  <div className="flex items-center justify-between pb-3 mb-3 border-b border-gray-100 dark:border-gray-700">
    <h5 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
      Tin nhắn
    </h5>
    <button
      onClick={toggleDropdown}
      className="text-gray-500 transition dropdown-toggle dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
    >
      <svg
        className="fill-current"
        width="24"
        height="24"
        viewBox="0 0 24 24"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.21967 7.28131C5.92678 6.98841 5.92678 6.51354 6.21967 6.22065C6.51256 5.92775 6.98744 5.92775 7.28033 6.22065L11.999 10.9393L16.7176 6.22078C17.0105 5.92789 17.4854 5.92788 17.7782 6.22078C18.0711 6.51367 18.0711 6.98855 17.7782 7.28144L13.0597 12L17.7782 16.7186C18.0711 17.0115 18.0711 17.4863 17.7782 17.7792C17.4854 18.0721 17.0105 18.0721 16.7176 17.7792L11.999 13.0607L7.28033 17.7794C6.98744 18.0722 6.51256 18.0722 6.21967 17.7794C5.92678 17.4865 5.92678 17.0116 6.21967 16.7187L10.9384 12L6.21967 7.28131Z"
        />
      </svg>
    </button>
  </div>

  <ul className="flex flex-col h-auto overflow-y-auto custom-scrollbar">
    {/* Sample message items */}
    <li>
      <DropdownItem
        onItemClick={closeDropdown}
        className="flex gap-3 rounded-lg border-b border-gray-100 p-3 px-4.5 py-3 hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-white/5"
      >
        <span className="relative block w-full h-10 rounded-full z-1 max-w-10">
          <Image
            width={40}
            height={40}
            src="/images/user/user-02.jpg"
            alt="User"
            className="w-full overflow-hidden rounded-full"
          />
        </span>
        <span className="block">
          <span className="mb-1 block text-sm font-medium text-gray-800 dark:text-white">
            Terry Franci
          </span>
          <span className="block text-sm text-gray-500 dark:text-gray-400">
            "Can we meet today to discuss the project?"
          </span>
          <span className="mt-1 block text-xs text-gray-400">5 phút trước</span>
        </span>
      </DropdownItem>
    </li>
    {/* Add more message items as needed */}
  </ul>

  {/* Button xem tất cả tin nhắn */}
  <div className="mt-auto pt-3 border-t border-gray-200 dark:border-gray-700">
    <Link
  href="/"
  className="block px-4 py-2 mt-3 text-sm font-medium text-center text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
  onClick={() => {
    alert("Đi đến trang tất cả tin nhắn!");
    closeDropdown();
  }}
>
  Xem tất cả tin nhắn
</Link>
  </div>
</Dropdown>
    </div>
  );
}