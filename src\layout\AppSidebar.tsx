import { useEffect, useState, useMemo, useCallback } from "react";
import { Sun, Moon, Box, ChevronLeft, ChevronRight } from "lucide-react";
import { RxDashboard } from "react-icons/rx";
import { LuTickets } from "react-icons/lu";
import { MdSupportAgent } from "react-icons/md";
import { FaRegObjectGroup, FaRobot } from "react-icons/fa";

import clsx from "clsx";
import { usePathname, useRouter } from "next/navigation";
import { useSidebar } from "@/context/SidebarContext";
import { useTheme } from "@/context/ThemeContext";
import { useAuth } from "@/context/AuthContext";
import { agentApi } from "@/api/agentApi";
import { Agent } from "@/types";
import React from "react";
import Link from "next/link";
import Image from "next/image";

export default function Sidebar() {
  const router = useRouter();
  const { theme, setThemeValue } = useTheme();
  const { user } = useAuth();
  
  const { isExpanded, isMobileOpen, isHovered, toggleSidebar, toggleMobileSidebar, activeItem, setActiveItem } = useSidebar();
  const pathname = usePathname();

  const handleToggle = () => {
    if (window.innerWidth >= 1024) {
      toggleSidebar();
    } else {
      toggleMobileSidebar();
    }
  };

  const [agentList, setAgentList] = useState<Agent[]>([])
  const [menu, setMenu] = useState<MenuSection[]>([])

  // Function to get icon based on function code
  const getIconForFunction = useCallback((code: string) => {
    // Check for specific function codes
    if (code.includes("DASHBOARD")) {
      return <RxDashboard className="w-5 h-5 opacity-70" />;
    }
    if (code.includes("TICKET")) {
      return <LuTickets className="w-5 h-5 opacity-70" />;
    }
    if (code.includes("AGENT")) {
      return <MdSupportAgent className="w-5 h-5 opacity-70" />;
    }
    if (code.includes("MODULE")) {
      return <FaRegObjectGroup className="w-5 h-5 opacity-70" />;
    }

    // Fallback based on parent codes
    switch (code) {
      case "FTI.AGENTAI.OVERVIEW":
      case "FTI.AGENTAI.OVERVIEW.DASHBOARD":
        return <RxDashboard className="w-5 h-5 opacity-70" />;
      case "FTI.AGENTAI.TICKET":
      case "FTI.AGENTAI.TICKET.TICKETS":
        return <LuTickets className="w-5 h-5 opacity-70" />;
      case "FTI.AGENTAI.AGENT":
      case "FTI.AGENTAI.AGENT.AGENTS":
        return <MdSupportAgent className="w-5 h-5 opacity-70" />;
      case "FTI.AGENTAI.SETTING":
      case "FTI.AGENTAI.SETTING.AGENTS":
        return <MdSupportAgent className="w-5 h-5 opacity-70" />;
      case "FTI.AGENTAI.SETTING.MODULE":
        return <FaRegObjectGroup className="w-5 h-5 opacity-70" />;
      default:
        return <Box className="w-5 h-5 opacity-70" />;
    }
  }, []);


  // Fetch agents on component mount
  // useEffect(() => {
  //   const fetchAgents = async () => {
  //       try{
  //           const agents = await agentApi.getAgents()
  //           if(agents != null)
  //           {
  //               setAgentList(agents)
  //           }
  //       }
  //       catch(err)
  //       {
  //           console.log(err)
  //       }
  //   }

  //   fetchAgents()
  // }, [])

  // Generate complete menu from user funcs and agents using useMemo
  const generatedMenu = useMemo(() => {
    if (user && user.funcs && user.funcs.length > 0) {
      return user.funcs.map((func) => {
        const baseItems = func.items.map((item) => ({
          name: item.displayName,
          icon: getIconForFunction(item.code),
          route: item.path
        }));

        // Add agents to agent management section
        // if ((func.displayName.toLowerCase().includes("agent") || func.code.includes("AGENT")) && agentList.length > 0) {
        //   const agentItems = agentList.map(agent => ({
        //     name: agent.name,
        //     icon: <FaRobot className="w-5 h-5 opacity-70" />,
        //     route: `/agent/${agent.code}`
        //   }));

        //   return {
        //     title: func.displayName.toLowerCase(),
        //     items: [...baseItems, ...agentItems]
        //   };
        // }

        return {
          title: func.displayName.toLowerCase(),
          items: baseItems
        };
      });
    }
    return [];
  }, [user, agentList]);

  // Update menu state when generated menu changes
  useEffect(() => {
    setMenu(generatedMenu);
  }, [generatedMenu]);

  // Fallback menu if user has no permissions
  const fallbackMenu: MenuSection[] = useMemo(() => [
    {
      title: "menu",
      items: [
        { name: "Dashboard", icon: <RxDashboard className="w-5 h-5 opacity-70" />, route: "/" },
      ],
    },
  ], []);

  const displayMenu = menu.length > 0 ? menu : fallbackMenu;

  return (
    <aside
      className={`fixed pt-6 flex flex-col lg:mt-0 top-0 px-5 left-0 bg-white dark:bg-gray-900 dark:border-gray-800 text-gray-900 h-screen transition-all duration-300 ease-in-out border-r border-gray-200 
        z-99999
        ${isExpanded || isMobileOpen
          ? "w-[290px]"
          : isHovered
            ? "w-[290px]"
            : "w-[90px]"
        }
        ${isMobileOpen ? "translate-x-0" : "-translate-x-full"}
        lg:translate-x-0`}
    >
      <div className="mb-8">
        <Link href="/">
          <div className="flex items-center gap-3">
            <Image
              className=""
              src="/agent-ai/images/logo/logo.png"
              alt="Logo"
              width={60}
              height={30}
            />
            <p className="text-xl font-bold text-black dark:text-gray-400">
              { (isExpanded || isMobileOpen) && "FTI Agent AI" }
            </p>
          </div>
        </Link>
   
          
        </div>

        {displayMenu.map((section, i) => (
          <div key={i} className="">
            {(isExpanded || isMobileOpen) && (
              <h4 className="text-gray-400 text-xs uppercase mb-4">
                {section.title}
              </h4>
            )}
            <ul className="mb-6 flex flex-col gap-3">
              {section.items.map((item, idx) => (
                <li
                  key={idx}
                  onClick={() => {
                    setActiveItem(item.name);
                    if (item.route) {
                      router.push(item.route);
                    }
                  }}
                  className={clsx(
                    "flex items-center rounded-md p-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-blue-light-900 dark:hover:text-gray-50",
                    activeItem === item.name && "bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-50",
                    activeItem !== item.name && "dark:text-gray-400"
                  )}
                >
                  {/* icon + name container */}
                  <div
                    className={clsx(
                      "flex relative",
                      !(isExpanded || isMobileOpen) ? "justify-center w-full" : "gap-3 font-normal"
                    )}
                  >
                    <div className="relative">
                      {item.icon ?? <Box size={18} />}
                      {item.badge && !(isExpanded || isMobileOpen) && (
                        <span className="absolute -top-1 -right-2 bg-green-600 text-white text-[10px] px-1 py-0.5 rounded-full font-bold leading-none">
                          {item.badge}
                        </span>
                      )}
                    </div>

                    {(isExpanded || isMobileOpen) && <span className="font-medium">{item.name}</span>}
                  </div>

                  {/* badge nếu sidebar mở */}
                  {item.badge && (isExpanded || isMobileOpen) && (
                    <span className="text-xs bg-green-600 text-white px-2 py-0.5 rounded-full font-bold">
                      {item.badge}
                    </span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        ))}

      {/* Footer - Theme + Collapse toggle */}
      {(isExpanded || isMobileOpen) && <div className="absolute left-0 bottom-15 w-full block items-center justify-center transition-all">
    <div className="flex justify-center gap-2 bg-gray-100 dark:bg-gray-800 p-2 rounded-xl w-40 m-auto">
        <button
          onClick={() => setThemeValue("light")}
          className={clsx(
            "flex items-center gap-1 px-2 py-1 rounded-md text-sm text-black",
            theme === "light" ? "bg-white shadow font-bold" : "text-blue-700 dark:text-blue-light-200"
          )}
        >
          <Sun size={16} />
          {(isExpanded || isMobileOpen) && "Light"}
        </button>
        <button
          onClick={() => setThemeValue("dark")}
          className={clsx(
            "flex items-center gap-1 px-2 py-1 rounded-md text-sm text-black",
            theme === "dark" ? "bg-white shadow font-bold" : "text-blue-700 dark:text-blue-light-200"
          )}
        >
          <Moon size={16} />
          {(isExpanded || isMobileOpen) && "Dark"}
        </button>
      </div>

      </div>}
      
      {/* Toggle button at bottom right */}
      <div className="absolute bottom-5 right-5">
        <button
          onClick={() => {handleToggle();}}
          className="p-2 bg-gray-100 hover:bg-gray-200 rounded-full shadow-sm transition-all duration-200"
          title={(isExpanded || isMobileOpen) ? "Expand" : "Collapse"}
        >
          {!(isExpanded || isMobileOpen) ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </button>
      </div>
    </aside>
  );
}

type MenuItem = {
  name: string;
  icon?: React.ReactNode;
  badge?: number;
  route?: string;
};

type MenuSection = {
  title: string;
  items: MenuItem[];
};
