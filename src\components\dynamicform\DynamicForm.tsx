"use client";
import { useState, ChangeEvent, FormEvent } from "react";

// Định nghĩa type cho từng trường form
export type FieldType = "text" | "number" | "date" | "email" | "password" | "select" | "textarea" | "file"; // có thể mở rộng nếu cần

export interface FieldOption {
  label: string;
  value: string | number;
}

export interface FieldConfig {
  label: string;
  name: string;
  type: FieldType;
  value?: string | number;
  placeholder?: string;
  options?: FieldOption[]; // dùng cho select, radio
}

export interface DynamicFormProps {
  fields: FieldConfig[];
  onSubmit?: (form: Record<string, string | number>) => void;
  showTitle?: boolean;
  title?: string;
}

export default function DynamicForm({ fields = [], onSubmit, showTitle = true, title = "Thông tin xác nhận hồ sơ" }: DynamicFormProps) {
  const [form, setForm] = useState<Record<string, string | number>>(
    fields.reduce((acc, cur) => ({ ...acc, [cur.name]: cur.value || "" }), {})
  );

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setForm((f) => ({
      ...f,
      [name]: type === "number" ? Number(value) : value,
    }));
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    onSubmit?.(form);
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="w-full space-y-6"
    >
      {showTitle && (
        <div className="text-center pb-4 border-b border-gray-100">
          <h2 className="text-xl font-bold text-gray-800 mb-2">{title}</h2>
          <p className="text-sm text-gray-600">Vui lòng điền đầy đủ thông tin bên dưới</p>
        </div>
      )}
      {fields.map((field) => (
        <div key={field.name} className="space-y-2">
          <label
            htmlFor={field.name}
            className="block text-sm font-semibold text-gray-700 cursor-pointer dark:text-gray-300"
          >
            {field.label}
            <span className="text-red-500 ml-1">*</span>
          </label>
          {field.type === "textarea" ? (
            <textarea
              name={field.name}
              id={field.name}
              value={form[field.name]}
              onChange={handleChange}
              className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 outline-none transition-all duration-200 resize-none min-h-[100px] bg-gray-50 focus:bg-white dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200"
              placeholder={field.placeholder || `Nhập ${field.label.toLowerCase()}`}
              rows={4}
            />
          ) : field.type === "select" && field.options ? (
            <select
              name={field.name}
              id={field.name}
              value={form[field.name]}
              onChange={handleChange}
              className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 outline-none transition-all duration-200 bg-gray-50 focus:bg-white appearance-none cursor-pointer dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200"
            >
              <option value="">Chọn {field.label.toLowerCase()}</option>
              {field.options.map((opt) => (
                <option key={opt.value} value={opt.value}>
                  {opt.label}
                </option>
              ))}
            </select>
          ) : (
            <input
              type={field.type}
              name={field.name}
              id={field.name}
              value={form[field.name]}
              onChange={handleChange}
              className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 outline-none transition-all duration-200 bg-gray-50 focus:bg-white dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200"
              placeholder={field.placeholder || `Nhập ${field.label.toLowerCase()}`}
              autoComplete="off"
            />
          )}
        </div>
      ))}

      <div className="pt-6 border-t border-gray-100">
        <button
          type="submit"
          className="w-full py-4 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] flex items-center justify-center gap-2"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          Hoàn tất và gửi
        </button>
      </div>
    </form>
  );
}
