"use client";
import React, { useEffect, useState } from "react";
import ComponentCard from "../common/ComponentCard";
import Button from "../ui/button/Button";
import { Modal } from "../ui/modal";
import Label from "../form/Label";
import Input from "../form/input/InputField";
import { useModal } from "@/hooks/useModal";
import { MdOutlineAddBox } from "react-icons/md";
import AgentCardSelector from "../card/AgentCardSelector";
import TextArea from "../form/input/TextArea";
import Image from "next/image";
import { ModuleApp } from "@/types";
import { moduleApi } from "@/api/moduleApi";
import Select from "../form/Select";
import Switch from "../form/switch/Switch";
import { ChevronDownIcon } from "lucide-react";
import { agentApi } from "@/api/agentApi";

interface CreateAgentModalProps {
  isSuccess: boolean,
  setIsSuccess: any
}
export default function CreateAgentModal({isSuccess, setIsSuccess} : CreateAgentModalProps) {
  const { isOpen, openModal, closeModal } = useModal();
  const [selected, setSelected] = useState<string>("chatbot");
  const [description, setDescription] = useState(""); 
  const [moduleId ,setModuleId] = useState("");
  const [moduleList, setModuleList] = useState<any>([])
  const [code, setCode] = useState("")
  const [agentName, setAgentName] = useState("")
  const [status, setStatus] = useState("public")
  const [difyKey, setDifyKey] = useState('')

  useEffect(() => {
      const fetchModuleList = async () => {
        try{
          const data = await moduleApi.getModules(true,1000,0);
          if(data)
          {
            setModuleList(data.map((value) => { return {label: value.name, value: value.id}}))
          }
        }catch(err)
        {
          console.log(err)
        }
      }
      
      fetchModuleList()
    }, [])
  
  const handleSave = async () => {
    try{
      const data = await agentApi.addAgent(Number(moduleId), code, difyKey,agentName, description, selected, status)
      if(data)
      {
        setIsSuccess(true)
        closeModal();
      }
    }
    catch(err){
      console.log(err)
    }
  };

  const handleSelectModuleChange = (value: string) => {
    setModuleId(value)
  }

  const handleSwitchChange = (checked: boolean) => {
    if(checked){
      setStatus("public")
    }else{
      setStatus("private")
    }
  }

  return (
    <div>
      <Button onClick={openModal} className="flex items-center p-2 px-6 border-none bg-gray-100 text-black font-semibold transition-all rounded-md gap-2 cursor-pointer">
        <MdOutlineAddBox  className="h-5 w-5"/>
        Tạo Agent
      </Button>
      <Modal
        isOpen={isOpen}
        onClose={closeModal}
        className="max-w-11/12 h-10/11"
      >
        <div className="flex justify-center overflow-y-auto overflow-x-hidden p-5 lg:p-10 h-full ">
          <div className="flex flex-row-reverse flex-1/2 pr-6 ">
            <div className=" ">
              {/* <div className="h-6 w-full 2xl:h-[100px]"></div> */}
              <h4 className="mb-6 text-lg font-medium text-gray-800 dark:text-white/90">
                Thông tin Agent
              </h4>
              <div className="mb-6">
                <AgentCardSelector selected={selected} setSelected={setSelected}/>
              </div>
              <div className="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-2">
                <div className="col-span-1 sm:col-span-2">
                  <Label>Tên Agent</Label>
                  <Input type="text" placeholder="Nhập tên Agent..." onChange={(e) => setAgentName(e.target.value)}/>
                </div>
                <div className="col-span-1">
                  <div>
                    <Label>Module</Label>
                    <div className="relative">
                      <Select
                        options={moduleList}
                        placeholder="Tên module (ví dụ SCS, RMD,...)"
                        onChange={(value) => handleSelectModuleChange(value)}
                        className="dark:bg-dark-900"
                      />
                      <span className="absolute text-gray-500 -translate-y-1/2 pointer-events-none right-3 top-1/2 dark:text-gray-400">
                          <ChevronDownIcon/>
                        </span>
                    </div>
                  </div>
                </div>
                <div className="col-span-1">
                  <Label>Code</Label>
                  <Input type="text" placeholder="Mã số hiệu Agent (ví dụ VBNA...)" onChange={(e) => setCode(e.target.value)}/>
                </div>
                <div className="col-span-1">
                  <Label>Phạm vi truy cập</Label>
                  <Switch
                    label="Public (Mọi user đều nhìn thấy)"
                    defaultChecked={true}
                    onChange={(checked) => handleSwitchChange(checked)}
                  />
                </div>
                <div className="col-span-1 sm:col-span-2">
                  <Label>Thông tin mô tả</Label>
                  <TextArea
                    value={description}
                    rows={6}
                    onChange={setDescription}
                    placeholder="Thông tin chi tiết về agent"
                    className="text-black"
                  />
                </div>
                <div className="col-span-1 sm:col-span-2">
                  <Label>Agent Key</Label>
                  <Input type="password" placeholder="Dify key" onChange={(e) => setDifyKey(e.target.value)}/>
                </div>
              </div>

              <div className="flex items-center justify-end w-full gap-3 mt-6">
                <Button size="sm" variant="outline" onClick={closeModal}>
                  Đóng
                </Button>
                <Button size="sm" onClick={handleSave}>
                  Lưu thay đổi
                </Button>
              </div>
          </div>
          </div>
          
          <div className="h-full border-l pl-6 flex-1/2 border-l-gray-100">
              <div className="h-6 w-full 2xl:h-[100px]"></div>
              <h4 className="uppercase font-semibold dark:text-gray-200">{selected}</h4>
              {
                selected == 'chatbot' ?<div>
                <p className="text-gray-500">Quickly build an LLM-based chatbot with simple configuration. You can switch to Chatflow later.
                </p>
                <Image
                    src="/agent-ai/images/agent/chatbot.png"
                    alt="404"
                    width={600}
                    height={200}
                  />
              </div>:
              <div>
                <p className="text-gray-500">
                  Workflow orchestration for multi-round complex dialogue tasks with memory capabilities.Learn more
                </p>
                <Image
                    src="/agent-ai/images/agent/workflow.png"
                    alt="404"
                    width={600}
                    height={200}
                  />
                </div>
              }
          </div>
        </div>
      </Modal>
    </div>
  );
}
