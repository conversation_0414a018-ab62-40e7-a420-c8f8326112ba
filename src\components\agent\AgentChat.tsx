"use client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aPaperPlane } from 'react-icons/fa';
import { HiDocumentText } from 'react-icons/hi';
import styles from './AgentChat.module.scss';
import React, { ReactNode, useEffect, useRef, useState } from 'react';
import { Agent, ChatMessage, FileItem, ServiceItem } from '@/types';

import { conversationApi } from '@/api/conversationApi';
import Image from 'next/image';
import MarkdownViewer from '../markdownviewer/MarkdownViewer';
import FileAttachmentDisplay from './FileAttachmentDisplay';
import { TbLayoutSidebarRightExpand, TbLayoutSidebarRightCollapse } from "react-icons/tb";
import { Modal } from '../ui/modal';
import { useModal } from '@/hooks/useModal';
import DynamicForm, { FieldConfig } from '../dynamicform/DynamicForm';
import VoiceInput from '../voiceinput/VoiceInput';
import { useSidebar } from '@/context/SidebarContext';
import { BiConversation } from "react-icons/bi";

type AgentChatProps = {
    children: ReactNode;
    messages: ChatMessage[],
    setMessages: React.Dispatch<React.SetStateAction<ChatMessage[]>>,
    files: FileItem[],
    setFiles: React.Dispatch<React.SetStateAction<FileItem[]>>,
    isEnabled: boolean,
    setIsEnabled: React.Dispatch<React.SetStateAction<boolean>>,
    listServices?: ServiceItem[],
    agent: Agent | null,
    loading: boolean,
    conversationId: number,
    agentCode: string | undefined,
    parametersRef: any | null,
    updateConversationNew: any,
    fetchFiles: any,
    generateTicket: any,
    loadMore: any,
    hasMore: boolean,
    isWidget: boolean | false, 
    openConversationMobile: any
};

function extractAttachmentInfo(message: string) {
  // Regex tìm phần [Đã đính kèm ...]
  const regex = /\[Đã đính kèm ([\d]+) file: ([^\]]+)\]/i;
  const match = message.match(regex);
  
  if (match) {
    const fileCount = parseInt(match[1]);
    // Tách danh sách file, trim từng tên
    const files = match[2].split(',').map(f => f.trim());
    // Trả về phần thông báo, danh sách file, và message đã tách phần này ra
    return {
      hasAttachment: true,
      fileCount,
      files,
      messageWithoutAttachment: message.replace(regex, '').trim()
    };
  }
  return {
    hasAttachment: false,
    fileCount: 0,
    files: [],
    messageWithoutAttachment: message
  };
}

const AgentChat: React.FC<AgentChatProps> = ({ children, agent, messages, setMessages, isEnabled,
    setIsEnabled, listServices = [], loading, conversationId, agentCode, parametersRef, updateConversationNew,
    fetchFiles,generateTicket, loadMore, hasMore, isWidget = false, openConversationMobile }) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [uploadFiles, setUploadFiles] = useState<File[]>([])
    const [input, setInput] = useState('');
    const [fileInput, setFileInput] = useState<FileItem[]>([])
    const [currentIndex, setCurrentIndex] = useState(0);
    const [showSidebar, setShowSidebar] = useState(false);
    var loadingMore = useRef(false);
    const {isMobile} = useSidebar();
    // Modal state for dynamic form
    const { isOpen: isFormModalOpen, openModal: openFormModal, closeModal: closeFormModal } = useModal();
    const [currentFormFields, setCurrentFormFields] = useState<FieldConfig[]>([]);

    const chatContainerRef = useRef<HTMLDivElement>(null);
    
    const scrollToBottom = () => {
        if(!loadingMore.current){
            const el = chatContainerRef.current;
            if (el) {
                el.scrollTop = el.scrollHeight;
            }
        }
        loadingMore.current  = false
    };
    
    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    useEffect(() => {
        const el = chatContainerRef.current;
        if (!el || loading) return;

        const handleScroll = async () => {
            
            if (el.scrollTop === 0 && hasMore) {
                loadingMore.current  = true

                const container = chatContainerRef.current;
                const oldScrollHeight = container?.scrollHeight ?? 0;
                
                await loadMore()
                console.log("A " + loadingMore.current)
                requestAnimationFrame(() => {
                    const newScrollHeight = el.scrollHeight;
                    el.scrollTop = newScrollHeight - oldScrollHeight;
                });
            }
        };

        el.addEventListener('scroll', handleScroll);
        return () => el.removeEventListener('scroll', handleScroll);
    }, [loading, hasMore]);


    const handleAnswer = async (key: string, value: string) => {
        parametersRef.current[key] = value
        console.log(parametersRef.current)
        setMessages([...messages, {
            "query": value,
            "answer": "(thinking)",
            "conversationId": 0,
            "createTicket": false,
            "id": ""
        }])

        if (currentIndex + 1 < listServices.length) {
            setCurrentIndex(prev => prev + 1);
        } else {
            try {
                if(!agentCode)
                {
                    return
                }
                const data = await conversationApi.sendMessage("Bắt đầu", conversationId, uploadFiles, agentCode, parametersRef.current)
                if (data != null) {
                    setMessages(prevMessages => {
                        const lastIndex = prevMessages.length - 1;

                        if (prevMessages[lastIndex]?.answer === '(thinking)'|| prevMessages[lastIndex]?.answer === '') {
                            const updatedMessages = [...prevMessages];
                            updatedMessages[lastIndex].answer = data.answer ? data.answer : "Hệ thống đang gặp sự cố phản hồi chậm...";
                            return updatedMessages;
                        }
                        return prevMessages; // fallback: không làm gì cả
                    });

                    updateConversationNew(data.conversationId)
                }
            } catch (err) {
                setMessages(prevMessages => {
                    const lastIndex = prevMessages.length - 1;

                    if (prevMessages[lastIndex]?.answer === '(thinking)') {
                        const updatedMessages = [...prevMessages];
                        updatedMessages[lastIndex].answer = "Hệ thống đang gặp sự cố phản hồi chậm. Vui lòng load lại trang...";
                        return updatedMessages;
                    }
                    return prevMessages; // fallback: không làm gì cả
                });
            }
            setIsEnabled(true);
            
        }
    };

    const handleUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (isEnabled) {
            const selectedFiles = e.target.files;
            if (selectedFiles) {
                const uploaded = Array.from(selectedFiles).map(file => ({
                    fileName: file.name,
                    lastModified: new Date().toLocaleString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                    }),
                    
                }));
                // setFiles(prev => [...uploaded, ...prev]);
                setFileInput(prev => [...uploaded, ...prev]);
                setUploadFiles(prev => [...selectedFiles, ...prev])
            }
        }

    };

    const handleRemoveFileInput = (name: string) => {
        const updatedFiles = fileInput.filter(file => file.fileName !== name);
        setFileInput(updatedFiles)
    }

    // Handle form submission from modal
    const handleFormSubmit = async (formData: Record<string, string | number>) => {
        closeFormModal();

        // Create a formatted message with form data
        const formMessage = Object.entries(formData)
            .map(([key, value]) => `${key}: ${value}`)
            .join('\n');

        const fullMessage = `Thông tin form đã điền:\n${formMessage}`;

        if (isEnabled) {
            setMessages(prev => [...prev, {
                "query": fullMessage,
                "answer": "(thinking)",
                "conversationId": 0,
                "createTicket": false,
                "id": ""
            }]);

            setIsEnabled(false);

            try {
                if (!agentCode) {
                    return;
                }
                const data = await conversationApi.sendMessage(fullMessage, conversationId, [], agentCode, parametersRef.current);
                if (data != null) {
                    if (data.createTicket == true) {
                        generateTicket(conversationId);
                    }
                    if (data.conversationId && conversationId == 0) {
                        updateConversationNew(data.conversationId);
                    }
                    setMessages(prevMessages => {
                        const lastIndex = prevMessages.length - 1;
                        if (prevMessages[lastIndex]?.answer === '(thinking)') {
                            const updatedMessages = [...prevMessages];
                            updatedMessages[lastIndex].answer = data.answer ? data.answer : "Hệ thống đang gặp sự cố phản hồi chậm...";
                            return updatedMessages;
                        }
                        return prevMessages;
                    });
                }
            } catch (err) {
                console.error("Error sending form data:", err);
                setMessages(prevMessages => {
                    const lastIndex = prevMessages.length - 1;

                    if (prevMessages[lastIndex]?.answer === '(thinking)') {
                        const updatedMessages = [...prevMessages];
                        updatedMessages[lastIndex].answer = "Hệ thống đang gặp sự cố phản hồi chậm. Vui lòng load lại trang...";
                        return updatedMessages;
                    }
                    return prevMessages; // fallback: không làm gì cả
                });
            }

            setIsEnabled(true);
        }
    };
    const onTranscript = (script: string) => {
        setInput(script)
    }
    // Handle form button click
    const handleFormClick = (fields: FieldConfig[]) => {
        setCurrentFormFields(fields);
        openFormModal();
    };


    const handleSend = async () => {
        if (isEnabled && (input.length > 0 || fileInput.length > 0)) {
            var inputValue = input
            if(fileInput.length > 0){
                // Add file attachment info to the message for API
                inputValue += `\n\n[Đã đính kèm ${fileInput.length} file: ${fileInput.map(f => f.fileName).join(', ')}]`;

                setMessages(prev => [...prev, {
                    "query": inputValue, // Keep original input for display
                    "answer": "(thinking-file)",
                    "conversationId": 0,
                    "createTicket": false,
                    "id": "",
                    "attachedFiles": fileInput // Store files separately for better UI rendering
                }])
            }
            else
                setMessages(prev => [...prev, {
                    "query": input,
                    "answer": "(thinking)",
                    "conversationId": 0,
                    "createTicket": false,
                    "id": ""
                }])
            setIsEnabled(false)
            setInput("")
            setFileInput([])

            try {
                if(!agentCode)
                {
                    return
                }
                const data = await conversationApi.sendMessage(inputValue, conversationId, uploadFiles, agentCode, parametersRef.current)
                if (data != null) {
                    if(data.createTicket == true)
                    {
                        generateTicket(conversationId)
                    }
                    if(data.conversationId && conversationId == 0)
                    {
                      updateConversationNew(data.conversationId)
                    }
                    setMessages(prevMessages => {
                        const lastIndex = prevMessages.length - 1;

                        if (prevMessages[lastIndex]?.answer === '(thinking)' || prevMessages[lastIndex]?.answer === '(thinking-file)') {
                            const updatedMessages = [...prevMessages];
                            updatedMessages[lastIndex].answer = data.answer ? data.answer : "Hệ thống đang gặp sự cố phản hồi chậm...";
                            updatedMessages[lastIndex].form = data.form
                            return updatedMessages;
                        }
                        return prevMessages;
                    });
                }

                if(uploadFiles.length > 0){
                    await fetchFiles(conversationId);
                }
            } catch (err) {
                setMessages(prevMessages => {
                    const lastIndex = prevMessages.length - 1;

                    if (prevMessages[lastIndex]?.answer === '(thinking)' || prevMessages[lastIndex]?.answer === '(thinking-file)') {
                        const updatedMessages = [...prevMessages];
                        updatedMessages[lastIndex].answer = "Hệ thống đang gặp sự cố phản hồi chậm. Vui lòng load lại trang...";
                        return updatedMessages;
                    }
                    return prevMessages; // fallback: không làm gì cả
                });
            }

            setUploadFiles([])
            setIsEnabled(true)
            if (fileInputRef.current) {
                fileInputRef.current.value = "";
            }
        }

    }

    return <main className="flex-1 flex flex-col border border-gray-200/60 bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-shadow duration-300 dark:bg-gray-900 dark:border-gray-700 h-full">
        <header className="border-b border-gray-200/60 dark:border-gray-700 p-4 flex items-center justify-between bg-white/80 backdrop-blur-sm dark:bg-gray-800">
            {isMobile && <div className='pe-4' onClick={()=> {openConversationMobile()}}>
                <BiConversation className='text-gray-400 w-6 h-6 cursor-pointer'/>
            </div>}
            <div className="flex flex-1 items-center">
                <div className="h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center mr-3 relative overflow-hidden shadow-lg dark:shadow-none" >
                    {/* <FaHeadset className="text-blue-600" /> */}
                    <Image
                        src="/agent-ai/images/agent/ai-ui.png"
                        alt="ai"
                        className='w-full'
                        fill={true}
                    />
                </div>
                <div>
                    <h2 className="font-semibold text-gray-700 dark:text-gray-200">{agent?.name}</h2>
                    <div className="flex items-center">
                        <span className="inline-block h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                        <span className="text-xs text-gray-500 dark:text-gray-300">Agent support: {agent?.code}</span>
                    </div>
                </div>
            </div>

            {/* Toggle button for sidebar */}
            {!isWidget && children && (
                <div className="flex items-center space-x-2">
                    <button
                        onClick={() => setShowSidebar(!showSidebar)}
                        className={`
                            p-2 rounded-lg transition-all duration-200
                            ${showSidebar
                                ? 'bg-blue-50 text-blue-600 hover:bg-blue-100'
                                : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                            }
                        `}
                        title={showSidebar ? "Ẩn thông tin" : "Hiển thị thông tin"}
                    >
                        {showSidebar?<TbLayoutSidebarRightCollapse fontSize={24}/> : <TbLayoutSidebarRightExpand fontSize={24}/>}
                    </button>
                </div>
            )}
        </header>
        <div className="flex-1 flex overflow-auto ">
            <div className={`flex-1 flex flex-col ${showSidebar && !isWidget ? 'border-r border-gray-200/60 dark:border-gray-600' : ''} bg-white dark:bg-gray-800 `}>
                <div className={`flex-1 overflow-y-auto p-4 ${styles.custom_scrollbar} space-y-4`}  ref={chatContainerRef}>
                    {loading == true && <div className={`${styles.chat_message_user} flex`}>
                        {[...Array(5)].map((_, i) => (
                            <div
                                key={i}
                                className="w-1 bg-gradient-primary rounded"
                                style={{
                                    animation: `equalizer 1s ease-in-out infinite`,
                                    animationDelay: `${i * 0.1}s`,
                                    height: "100%",
                                }}
                            />
                        ))}</div>}
                    {
                        loading == false && messages.map((value: any, index: number) => {
                            const queryInfo = extractAttachmentInfo(value.query)
                            const answer = value.answer
                            const form = value.form
                            
                            return <div key={index} className='space-y-4'>
                                <div className="flex justify-end">
                                    <div className={`${styles.chat_message_user} px-4 py-3 max-w-xs lg:max-w-md min-w-0`}>
                                        <div className="break-words">
                                            <MarkdownViewer content={queryInfo.messageWithoutAttachment} className='text-white font-medium'/>
                                        </div>
                                        {queryInfo.fileCount > 0 && <span className="text-xs font-medium text-white">📁 File đính kèm: {queryInfo.fileCount}</span>}
                                        {/* <div className={`max-w-xs lg:max-w-md min-w-0`}>
                                            <div className="w-full ">
                                                <FileAttachmentDisplay files={queryInfo.files} />
                                            </div>
                                        </div> */}
                                    </div>
                                    
                                </div>
                                {queryInfo.hasAttachment && queryInfo.fileCount > 0 && (
                                    <div className="flex justify-end">
                                        <div className={`max-w-xs lg:max-w-md mt-[-20px] min-w-0`}>
                                            <div className="w-full ">
                                                <FileAttachmentDisplay files={queryInfo.files} />
                                            </div>
                                        </div>
                                    </div>
                                )}
                                {
                                    (answer != '(thinking)' && answer != '(thinking-file)' && answer != null && answer.length > 0) ? <div className="flex">
                                        <div className="flex-shrink-0 mr-3">
                                            <div className="h-8 w-8 rounded-full bg-gradient-primary flex items-center justify-center relative overflow-hidden shadow-md shadow-sky-200 dark:shadow-none">
                                                <Image
                                                    src="/agent-ai/images/agent/ai-ui.png"
                                                    alt="ai"
                                                    className='w-full'
                                                    fill={true}
                                                />
                                            </div>
                                        </div>
                                        <div className={`${styles.chat_message_ai} px-4 py-3 max-w-xs lg:max-w-md dark:bg-gray-700 bg-[#F2F4F7] `}>
                                            {/* <p className="text-gray-800">{answer}</p> */}
                                            <MarkdownViewer content={answer} className=""/>
                                            {
                                                form && <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl shadow-sm">
                                                    <div className="flex items-center gap-3 mb-3">
                                                        <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow-md">
                                                            <HiDocumentText className="w-4 h-4 text-white" />
                                                        </div>
                                                        <div>
                                                            <h4 className="font-semibold text-gray-800 text-sm">Form đính kèm</h4>
                                                            <p className="text-xs text-gray-600">Nhấn để điền thông tin</p>
                                                        </div>
                                                    </div>
                                                    <button
                                                        onClick={() => handleFormClick(JSON.parse(form))}
                                                        className="w-full px-4 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-medium rounded-lg transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-[1.02] flex items-center justify-center gap-2"
                                                    >
                                                        <HiDocumentText className="w-4 h-4" />
                                                        Mở form điền thông tin
                                                    </button>
                                                </div>
                                            }
                                            {/* <p className="text-xs text-gray-500 mt-2">AI Agent • Just now</p> */}
                                        </div>
                                    </div> : (answer != null && answer.length > 0) ? <div className="flex gap-1 items-center h-6">
                                        {[...Array(5)].map((_, i) => (
                                            <div
                                                key={i}
                                                className="w-1 bg-gradient-primary rounded"
                                                style={{
                                                    animation: `equalizer 1s ease-in-out infinite`,
                                                    animationDelay: `${i * 0.1}s`,
                                                    height: "100%",
                                                }}
                                            />
                                        ))}
                                        { answer == "(thinking)" && <div className='opacity-50 text-sm ml-2 dark:text-gray-200'>Đang suy nghĩ...</div>}
                                        { answer == "(thinking-file)" && <div className='opacity-50 text-sm ml-2 dark:text-gray-200'>Đang đọc file...</div>}
                                    </div> : ""
                                }

                            </div>
                        })
                    }
                    {
                        (loading == false && Object.keys(parametersRef.current).length < listServices.length
                            && listServices.length > 0) && (
                            <div className="flex">
                                <div className="flex-shrink-0 mr-3">
                                    <div className="h-8 w-8 rounded-full bg-gradient-primary flex items-center justify-center relative overflow-hidden shadow-md shadow-sky-200 dark:shadow-none">
                                        <Image
                                            src="/agent-ai/images/agent/ai-ui.png"
                                            alt="ai"
                                            className='w-full'
                                            fill={true}
                                        />
                                    </div>
                                </div>
                                <div className={`${styles.chat_message_ai} px-4 py-3 max-w-xs lg:max-w-md bg-[#F2F4F7] dark:bg-gray-700`}>
                                    <p className="text-gray-800 dark:text-gray-200">
                                        {listServices[currentIndex].question}
                                    </p>
                                    <div className="mt-3 flex flex-wrap gap-2">
                                        {listServices[currentIndex].options.map((option: string, index: number) => (
                                            <button
                                                key={index}
                                                className="text-xs bg-blue-300 hover:bg-blue-400 text-blue-900 px-3 py-1 rounded-full transition-colors duration-300"
                                                onClick={() => handleAnswer(listServices[currentIndex].key, option)}
                                            >
                                                {option}
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        )
                    }

                    <div ref={messagesEndRef} />
                </div>

                <footer className="p-4 border-t border-gray-200/60 w-full bg-gray-50/30 dark:bg-gray-800 dark:border-gray-700">
                    <div className="border border-gray-300/60 rounded-xl px-4 py-3 w-full focus-within:border-blue-400 focus-within:ring-2 focus-within:ring-blue-100 transition-all duration-300 bg-white dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700">
                        <input value={input}
                            onChange={e => setInput(e.target.value)}
                            onKeyDown={e => e.key === "Enter" && handleSend()}
                            placeholder="Type your message..."
                            className="w-full focus:outline-none focus:ring-0 focus:border-transparent resize-none dark:placeholder:text-gray-100 placeholder:text-gray-800"
                        />
                        <input
                            ref={fileInputRef}
                            type="file"
                            className="hidden"
                            onChange={handleUpload}
                            multiple
                            disabled={!isEnabled}
                        />
                        {/* File attachments display - above input */}
                        

                        <div className='flex justify-between items-end'>
                            {fileInput.length > 0 && (
                            <div className="mb-3 max-h-20 overflow-y-auto">
                                <div className="flex items-center gap-1 mb-1">
                                    <span className="text-xs text-gray-600">📎 {fileInput.length} file{fileInput.length > 1 ? 's' : ''}</span>
                                </div>
                                <div className="flex flex-wrap gap-1">
                                    {fileInput.map((value, index) => {
                                        const fileExtension = value.fileName.split('.').pop()?.toLowerCase() || '';
                                        const getFileIcon = (ext: string) => {
                                            switch(ext) {
                                                case 'pdf': return '📄';
                                                case 'doc': case 'docx': return '📝';
                                                case 'xls': case 'xlsx': return '📊';
                                                case 'ppt': case 'pptx': return '📋';
                                                case 'jpg': case 'jpeg': case 'png': case 'gif': case 'bmp': return '🖼️';
                                                case 'mp4': case 'avi': case 'mov': case 'wmv': return '🎥';
                                                case 'mp3': case 'wav': case 'flac': return '🎵';
                                                case 'zip': case 'rar': case '7z': return '🗜️';
                                                case 'txt': return '📄';
                                                default: return '📎';
                                            }
                                        };

                                        return <div key={index} className='flex items-center gap-1 px-2 py-1 rounded-lg bg-blue-100 text-blue-800 text-xs max-w-32'>
                                            <span className='text-sm'>{getFileIcon(fileExtension)}</span>
                                            <span className='truncate' title={value.fileName}>
                                                {value.fileName.length < 10 ? value.fileName : value.fileName.substring(0,8) + "..."}
                                            </span>
                                            <button
                                                className='flex-shrink-0 w-4 h-4 rounded-full bg-red-200 hover:bg-red-300 text-red-700 text-xs font-bold cursor-pointer transition-colors duration-200 flex items-center justify-center ml-1'
                                                onClick={() => handleRemoveFileInput(value.fileName)}
                                                title="Xóa file"
                                            >
                                                ×
                                            </button>
                                        </div>
                                    })}
                                </div>
                            </div>
                        )}
                        {fileInput.length == 0 && <div className='flex-1'></div>}
                            <div className={`flex items-center space-x-4 shrink-0`}>
                                <VoiceInput onTranscript={onTranscript} enabled={isEnabled}/>
                                <button className={`text-gray-500 hover:text-sky-600 transition-colors duration-300 ${!isEnabled && 'opacity-55 cursor-not-allowed'}`} onClick={() => fileInputRef.current?.click()}><FaPaperclip fontSize={18}/></button>
                                <button id="send-btn" className={`bg-gradient-secondary text-white px-4 py-1 rounded-lg hover:bg-gradient-primary-hover transition-all duration-300 shadow-lg ${!isEnabled && 'opacity-55 cursor-not-allowed'}`} onClick={handleSend}>
                                    Gửi <FaPaperPlane className="ml-1 inline" />
                                </button>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
            {/* Integrated Sidebar - Facebook/Workplace style */}
            {!isWidget && showSidebar && children && (
                <>
                    {/* Mobile overlay */}
                    {/* <div
                        className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
                        onClick={() => setShowSidebar(false)}
                    /> */}
                    <div className={`flex-1 overflow-y-auto min-w-[200px] max-w-1/3`}>
                        {/* <div className="px-4 py-3 border-b border-gray-200/60 bg-white/80 backdrop-blur-sm">
                            <div className="flex items-center justify-between">
                                <h3 className="font-medium text-gray-500 text-sm">Thông tin cuộc trò chuyện</h3>
                                <button
                                    onClick={() => setShowSidebar(false)}
                                    className="p-1.5 rounded-lg hover:bg-gray-100/80 transition-colors duration-200 text-gray-400 hover:text-gray-600"
                                    title="Đóng"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div> */}
                        <div className='p-4 space-y-4 bg-gray-100/80 h-full dark:bg-gray-800 dark:border-gray-700'>
                            {children}
                        </div>
                    </div>
                </>
            )}
        </div>

        {/* Dynamic Form Modal */}
        <Modal
            isOpen={isFormModalOpen}
            onClose={closeFormModal}
            className="max-w-2xl p-6 lg:p-8 overflow-y-auto max-h-[90vh]"
        >
            <div className="space-y-6">
                <div className="flex items-center gap-3 pb-4 border-b border-gray-200">
                    <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow-md">
                        <HiDocumentText className="w-5 h-5 text-white" />
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Điền thông tin form</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Vui lòng điền đầy đủ thông tin bên dưới</p>
                    </div>
                </div>

                {currentFormFields.length > 0 && (
                    <DynamicForm
                        fields={currentFormFields}
                        onSubmit={handleFormSubmit}
                        showTitle={false}
                    />
                )}
            </div>
        </Modal>
    </main>
}

export default AgentChat