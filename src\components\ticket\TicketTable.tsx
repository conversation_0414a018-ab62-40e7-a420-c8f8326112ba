import React, { useEffect, useState } from 'react';
import { FaSearch } from 'react-icons/fa';
import clsx from 'clsx';
import axios from 'axios';
import { Agent, Ticket, TicketPagination } from '@/types';
import { ticketApi } from '@/api/ticketApi';
import Link from 'next/link';
import { AppRouting } from '@/constants/routes';
import { getPaginationRange } from '@/utils/pagination';

// Định nghĩa kiểu dữ liệu ticket
// Props: agent để gọi API
interface TicketTableProps {
  agent: Agent | null;
}

const statusColors: Record<string, string> = {
  'Chờ xử lý': 'bg-yellow-500',
  'Đã đóng': 'bg-gray-500',
  'Bị từ chối': 'bg-red-500',
  undefined: 'bg-gray-500'
};


const TicketTable: React.FC<TicketTableProps> = ({ agent }) => {
  const [tickets, setTickets] = useState<TicketPagination | null>(null);
  const [page, setPage] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>('Không thể lấy danh sách ticket.');
  const [filterStatus, setFilterStatus] = useState<string>('')
  const [search, setSearch] = useState<string>('')

  const fetchTickets = async () => {
    if (!agent) return;
    setLoading(true);
    setError(null);
    try {
      const res = await ticketApi.getTicketPagination(undefined, search, filterStatus, 10, (page - 1)* 10);
      setTickets(res);
    } catch (err) {
      setError('Không thể lấy danh sách ticket.');
    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {
    fetchTickets();
  }, [agent, filterStatus, page]);

  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
        fetchTickets();
      }
  }
  const handleStatusFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilterStatus(e.target.value)  
  }


  // if (loading) return <div className="p-4">Đang tải dữ liệu...</div>;
  if (error || !tickets) return <div className="p-4 text-red-600">{error}</div>;

  return (
    <div className="p-2 px-4 bg-gray-50">
      {/* Filters */}
      <div className="flex flex-wrap gap-3 mb-4 items-center">
        <div className="relative">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            onKeyDown={handleSearchKeyDown}
            placeholder="Tìm kiếm yêu cầu"
            className="pl-10 pr-4 py-2 border rounded-lg bg-white shadow-sm"
          />
        </div>
        {/* <select className="px-4 py-2 border rounded-lg bg-white shadow-sm">
          <option>Chọn Loại dịch vụ</option>
        </select> */}
        <select className="px-4 py-2 border rounded-lg bg-white shadow-sm" value={filterStatus} onChange={handleStatusFilter}>
          <option value='default' disabled>Chọn trạng thái</option>
          <option value=''>Tất cả</option>
          <option value='Chờ xử lý'>Chờ xử lý</option>
          <option value='Bị từ chối'>Bị từ chối</option>
          <option value='Đã đóng'>Đã đóng</option>
        </select>
        {/* <select className="px-4 py-2 border rounded-lg bg-white shadow-sm">
          <option>Chọn Ngày tạo</option>
        </select> */}
      </div>

      {/* Table */}
      <div className="overflow-auto bg-white shadow rounded-lg">
        <table className="min-w-full text-sm text-left border-collapse">
          <thead className="bg-gray-100 text-gray-700">
            <tr>
              <th className="p-3">Mã yêu cầu</th>
              <th className="p-3">Người yêu cầu</th>
              <th className="p-3">Tool tiếp nhận</th>
              <th className="p-3">Ngày tạo</th>
              {/* <th className="p-3">Ngày cập nhật</th> */}
              <th className="p-3">Trạng thái</th>
            </tr>
          </thead>
          <tbody>
            {tickets.data.map((ticket) => (
              <tr key={ticket.id} className="border-b">
                <td className="p-3"><Link className='transition-all text-blue-800 hover:text-yellow-600' href={`${AppRouting.ticketDetailPage}/${ticket.id}`}>{ticket.title}</Link></td>
                <td className="p-3">{ticket.createdBy || 'Không rõ'}</td>
                <td className="p-3">{ticket.moduleName ?? 'N/A'}</td>
                <td className="p-3">
                  {new Date(ticket.createdAt).toLocaleString()}
                </td>
                {/* <td className="p-3">
                  {ticket.updated_at
                    ? new Date(ticket.updated_at).toLocaleString()
                    : 'Chưa cập nhật'}
                </td> */}
                <td className="p-3">
                  <span
                    className={clsx(
                      'text-white px-3 py-1 rounded-full text-xs font-medium',
                      statusColors[ticket.overallStatus ? ticket.overallStatus : "Chờ xử lý"]
                    )}
                  >
                    {ticket.overallStatus ? ticket.overallStatus : "Chờ xử lý"}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="mt-4 flex justify-between items-center">
        <p className="text-sm text-gray-600">
          Hiển thị từ {1 + (tickets.pageIndex - 1) * tickets.pageSize} đến{' '}
          {Math.min(page * tickets.pageSize, tickets.totalCount)} trên tổng số {tickets.totalCount} kết quả
        </p>
        <div className="gradient-pagination">
          <button
            onClick={() => setPage((p) => Math.max(1, p - 1))}
            disabled={page === 1}
            className="gradient-pagination-button"
          >
            Trước
          </button>
          {getPaginationRange(page, Math.ceil(tickets.totalCount / tickets.pageSize)).map((p, idx) =>
            p === '...' ? (
              <span key={idx} className="gradient-pagination-ellipsis">...</span>
            ) : (
              <button
                key={p}
                onClick={() => setPage(p as number)}
                className={clsx(
                  'gradient-pagination-button',
                  p === page ? 'active' : ''
                )}
              >
                {p}
              </button>
            )
          )}
          <button
            onClick={() =>
              setPage((p) => (p < Math.ceil(tickets.totalCount / tickets.pageSize) ? p + 1 : p))
            }
            disabled={page >= Math.ceil(tickets.totalCount / tickets.pageSize)}
            className="gradient-pagination-button"
          >
            Sau
          </button>
        </div>
      </div>
    </div>
  );
};

export default TicketTable;
