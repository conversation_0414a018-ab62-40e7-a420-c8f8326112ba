import { TicketStatus } from "@/enums/AgentCode";
import { TicketApprovalStatus } from "@/types";

type TimelineItemProps = {
  data: TicketApprovalStatus
  end: boolean;
};

const statusColors: Record<string, string> = {
  'Open' : 'bg-yellow-500',
  'Closed': 'bg-green-500',
  'Rejected': 'bg-red-500',
  'Cancelled': 'bg-gray-500',
  'InProgress': 'bg-blue-500',
  'New': 'bg-green-500',
};

export const TimelineItem = ({ data, end }: TimelineItemProps) => {
  const statusName: Record<string, string> = {
    'Open': 'Chờ xử lý',
    'Closed': 'Hoàn thành',
    'InProgress': 'Đang xử lý',
    "Rejected": 'Từ chối',
    "Cancelled": 'Hủy',
    "New": 'Tạo mới ',
  };
  return (
    <div className="flex gap-3 mb-4 w-full">
      {/* Cột trái: Dot + Line */}
      <div className="flex flex-col items-center">
       {
         (data.approvedBy || data.status === "New") ? (
          <div className={`w-3 h-3 ${statusColors[data.status]} rounded-full mt-1.5`} />
        ) : (
          <div className={`w-3 h-3 ${statusColors["Cancelled"]} rounded-full mt-1.5`} />
        )
       }
        {!end && <div className="w-px flex-1 bg-gray-300 mt-2" />}
      </div>

      {/* Nội dung */}
      <div className="flex justify-between b-2 w-full">
        
        <div className="flex-1">
          <div className="text-sm text-gray-900 font-medium mb-1 dark:text-gray-100">{data.name}</div>
          <div className="text-xs text-gray-500 mb-1 dark:text-gray-300">
            {data.updatedAt ? new Date(data.updatedAt).toLocaleString() : new Date(data.createdAt).toLocaleString()}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">
            {data.userId}
          </div>
        </div>
        {
          data.approvedBy && (
            <div className="">
              <div className="text-sm text-gray-900 font-medium mb-1 flex justify-end dark:text-gray-100">{statusName[data.status]}</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">{data.approvedBy}</div>
            </div>
          )
        }
       
      </div>
    </div>
  );
};
