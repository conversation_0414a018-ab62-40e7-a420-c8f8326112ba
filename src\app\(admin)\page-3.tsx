"use client";

import React, { useEffect, useRef, useState } from "react";
import { ticketApi } from "@/api/ticketApi";
import { agentApi } from "@/api/agentApi";
import { BookmarkIcon as BookmarkSolidIcon } from "@heroicons/react/24/solid";
import { BookmarkIcon as BookmarkOutlineIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";
import { FaSearch } from "react-icons/fa";
import Link from "next/link";

const statusColor: Record<string, string> = {
  'Chờ xử lý': 'bg-yellow-300',
  'Đã đóng': 'bg-gray-300',
  'Bị từ chối': 'bg-red-300',
  undefined: 'bg-gray-500'
};


const options = [
  { value: "favorite", label: "Sort by: Favorite" },
];

function CustomSortDropdown({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) {
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  
  // Đóng dropdown khi click ra ngoài
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const selectedOption = options.find((o) => o.value === value);

  return (
    <div className="relative w-40" ref={ref}>
      <button
        type="button"
        onClick={() => setOpen(!open)}
        className="w-full border border-gray-300 rounded-lg px-2 py-1 text-sm bg-white flex items-center gap-2 justify-start focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <span style={{ color: "#001A72", fontSize: "1.4em", lineHeight: 1 }}>
          ●
        </span>
        <span>{selectedOption?.label || "Select option"}</span>
      </button>
      {open && (
        <ul className="absolute w-full mt-1 border border-gray-300 rounded bg-white shadow z-10 max-h-60 overflow-auto">
          {options.map((option) => (
            <li
              key={option.value}
              onClick={() => {
                onChange(option.value);
                setOpen(false);
              }}
              className={`cursor-pointer px-3 py-1 flex items-center gap-2 hover:bg-blue-100 ${
                option.value === value ? "font-semibold bg-blue-50" : ""
              }`}
            >
              <span
                style={{
                  color: "#001A72",
                  fontSize: "1.4em",
                  lineHeight: 1,
                  visibility: option.value === value ? "visible" : "hidden",
                }}
              >
                ●
              </span>
              {option.label}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default function Home() {
  const [applications, setApplications] = useState<any[]>([]);
  const [tickets, setTickets] = useState<any[]>([]);
  const [loadingApplications, setLoadingApplications] = useState(true);
  const [loadingTickets, setLoadingTickets] = useState(true);

  const [sortOption, setSortOption] = useState("favorite");
  const [searchAgent, setSearchAgent] = useState<string>('')

  const router = useRouter();

  const toggleFavorite = (name: string) => {
    setApplications((apps) => {
      return apps.map(app =>
        app.name === name ? { ...app, isFavorite: !app.isFavorite } : app
      );
  });
};

  const getSortedApplications = () => {
    const apps = [...applications]; // ← dùng const
    if (sortOption === "favorite") {
      apps.sort((a, b) => (b.isFavorite === a.isFavorite ? 0 : b.isFavorite ? -1 : 1));
    }
    return apps;
  };
  const handleSearchAgentKeyDown =(e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
        // fetchTickets();
      }
  }
  useEffect(() => {
    const fetchAgents = async () => {
      try {
        const agents = await agentApi.getAgents();
        // Chuyển dữ liệu API về dạng cần hiển thị, ví dụ:
        const formattedApps = agents.map((agent) => ({
          agentCode: agent.code,
          name: agent.name,
          tickets: Math.floor(Math.random() * 20), // giả sử tạm tickets random vì API ko có
          description: agent.description,
          isFavorite: agent.name.includes("SCS"), // ví dụ đánh dấu favorite nếu có SCS
        }));
        setApplications(formattedApps);
      } catch (error) {
        console.error("Error fetching agents:", error);
      } finally {
        setLoadingApplications(false);
      }
    };
    fetchAgents();
  }, []);

  
useEffect(() => {
  const fetchTickets = async () => {
    try {
      // Nếu muốn lấy tất cả ticket thì để moduleId = undefined hoặc 0
      // Ví dụ, nếu có state selectedModuleId thì thay đổi moduleId theo đó
      const moduleId = undefined; // hoặc 1, 2, 3,... tùy chọn lọc
      const result = await ticketApi.getTickets(moduleId);

      const formatted = result.map((item: any) => ({
        id: item.id,
        title: item.title,
        requester: item.createdBy,
        tool: item.moduleName,
        created: new Date(item.createdAt).toLocaleString(),
        received: new Date(item.updatedAt || item.createdAt).toLocaleString(),
        status: item.overallStatus || "Chờ xử lý",
      }));

      setTickets(formatted);
    } catch (error) {
      console.error("Error fetching tickets:", error);
    } finally {
      setLoadingTickets(false);
    }
  };

  fetchTickets();
}, []);
    return (
    <div className="space-y-10">
      {/* Applications Section */}
      <div>
        <div className="flex justify-between items-center mb-4">
          {/* Bên trái */}
          <div className="flex items-center gap-6">
            <h2 className="text-xl font-bold">Applications</h2>

            <div className="relative ms-6">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchAgent}
                onChange={(e) => setSearchAgent(e.target.value)}
                onKeyDown={handleSearchAgentKeyDown}
                placeholder="Tìm kiếm agent"
                className="pl-10 pr-4 py-2 border rounded-lg bg-white shadow-sm w-72 h-10"
              />
            </div>

            <select className="border py-2 border-gray-300 rounded-lg px-2 text-sm shadow-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 w-40 h-10 text-gray-500">
              <option disabled>Chọn Loại agent</option>
              <option>Tất cả</option>
              <option>Chatbot</option>
              <option>Workflow</option>
            </select>
          </div>

          {/* Bên phải */}
          {/* <div className="flex items-center gap-6">
            <CustomSortDropdown value={sortOption} onChange={setSortOption} />
            <button className="text-blue-600 text-sm hover:underline">See all</button>
          </div> */}
        </div>
        <div className="">
          <div className="bg-white shadow-lg rounded-xl relative p-4 space-y-2 border border-gray-200">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 px-4">
              {loadingApplications ? (
                <p>Đang tải dữ liệu applications...</p>
              ) : applications.length === 0 ? (
                <p>Không có ứng dụng nào.</p>
              ) : (
                getSortedApplications().map((app) => {
                  console.log(app);
                  return <div
                    key={app.name}
                    className="cursor-pointer"
                    onClick={() => {
                      router.push(`/agent/${app.agentCode}`);
                    }}
                  >
    {/* Nội dung card app */}
    <div className="flex gap-4">
      <div className="w-16 h-16 bg-gray-300 rounded flex items-center justify-center text-2xl font-bold text-black">
        {app.name.includes("SCS") ? "SCS" : "AI"}
      </div>
      <div className="flex-1 flex flex-col">
        <div className="flex items-center gap-40 text-lg font-semibold">
          <span className="truncate">{app.name}</span>
          {app.isFavorite ? (
            <BookmarkSolidIcon
              onClick={(e) => {
                e.stopPropagation(); // ngăn không cho onClick cha chạy khi click icon
                toggleFavorite(app.name);
              }}
              className="shrink-0 cursor-pointer text-[#001A72]"
              style={{ width: 24, height: 24 }}
              title="Unfavorite"
            />
          ) : (
            <BookmarkOutlineIcon
              onClick={(e) => {
                e.stopPropagation();
                toggleFavorite(app.name);
              }}
              className="shrink-0 cursor-pointer text-gray-400"
              style={{ width: 24, height: 24 }}
              title="Favorite"
            />
          )}
        </div>
        <p className="text-sm text-gray-600 line-clamp-2 pr-10 break-words">
          {app.description}
        </p>
        <p className="text-green-500 text-sm font-medium">
          ● {app.tickets} Ticket
        </p>
      </div>
    </div>
  </div>
})
      )}
    </div>
  </div>
</div>
</div>

      {/* Tickets Section */}
      <div>
        <div className="flex items-center justify-between mb-4">
  <div className="flex items-center gap-10">
    <h2 className="text-xl font-bold">My Ticket</h2>
    {/* <div className="relative w-96">
  <span className="absolute inset-y-0 left-3 flex items-center pointer-events-none text-gray-400">
    <svg
      className="w-4 h-4"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 1110.5 3a7.5 7.5 0 016.15 13.65z"
      />
    </svg>
  </span>
  <input
  type="text"
  placeholder="Search"
  className="w-full border border-gray-300 rounded-lg px-10 py-1 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
/>
</div> */}
  </div>
  <Link className="text-blue-600 text-sm hover:underline" href={"ticket"}>See all</Link>
</div>
<div className="">
        <div className="bg-white rounded-4xl shadow-lg border p-4">
  <div className="overflow-auto">
    <table
      className="w-full text-sm border-separate"
      style={{ borderSpacing: '0 14px' }}
    >
<thead>
  <tr className="text-left">
    <th className="px-10 py-2">Mã yêu cầu</th>
    <th className="px-10 py-2">Người yêu cầu</th>
    <th className="px-10 py-2">Tool tiếp nhận</th>
    <th className="px-10 py-2">Ngày tạo</th>
    <th className="px-10 py-2">Ngày tiếp nhận</th>
    <th className="px-10 py-2">Trạng thái</th>
  </tr>
</thead>
<tbody>
  {loadingTickets ? (
    <tr>
      <td colSpan={6} className="text-center py-4 text-gray-500">
        Đang tải dữ liệu...
      </td>
    </tr>
  ) : tickets.length === 0 ? (
    <tr>
      <td colSpan={6} className="text-center py-4 text-gray-500">
        Không có ticket nào.
      </td>
    </tr>
  ) : (
    tickets.map((ticket, i) => (
      <tr
        key={i}
        className="bg-white shadow-md hover:bg-gray-50 transition duration-150 cursor-pointer"
        onClick={() => router.push(`/ticket/detail/${ticket.id}`)}
      >
        <td className="px-10 py-3 rounded-l-lg text-blue-900">{ticket.title}</td>
        <td className="px-10 py-3">{ticket.requester}</td>
        <td className="px-10 py-3">{ticket.tool}</td>
        <td className="px-10 py-3">{ticket.created}</td>
        <td className="px-10 py-3">{ticket.received}</td>
        <td className="px-10 py-3 rounded-r-lg">
          <span
            className={`px-2 py-1 rounded text-xs font-semibold ${
              statusColor[ticket.status] || "bg-gray-500 text-white"
            }`}
          >
            {ticket.status}
          </span>
        </td>
      </tr>
    ))
  )}
</tbody>
    </table>
  </div>
</div>
</div>
      </div>
    </div>
  );
}