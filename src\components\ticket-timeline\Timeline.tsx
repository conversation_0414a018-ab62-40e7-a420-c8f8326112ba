import { TicketStatus } from '@/enums/AgentCode';
import { TimelineItem } from './TimelineItem';
import {TicketApprovalStatus} from '@/types/index';


interface TimelineProps {
    timelineData: TicketApprovalStatus[]
}

export const Timeline = ({timelineData} : TimelineProps) => {
  return (
    <div className="w-full">
      <div className="space-y-2">
        {timelineData.map((item, index) => (
          <TimelineItem key={index} data={item} end={index == timelineData.length -1} />
        ))}
      </div>
    </div>
  );
}