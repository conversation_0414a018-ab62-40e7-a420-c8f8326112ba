"use client";
import React, { useEffect, useState } from "react";
import ComponentCard from "../common/ComponentCard";
import Button from "../ui/button/Button";
import { Modal } from "../ui/modal";
import Label from "../form/Label";
import Input from "../form/input/InputField";
import { useModal } from "@/hooks/useModal";
import { MdOutlineAddBox } from "react-icons/md";
import { Agent, ModuleApp } from "@/types";
import AgentCardSelector from "../card/AgentCardSelector";
import TextArea from "../form/input/TextArea";
import Image from "next/image";
import { moduleApi } from "@/api/moduleApi";
import { agentApi } from "@/api/agentApi";
import Select from "../form/Select";
import { ChevronDownIcon } from "lucide-react";
import Switch from "../form/switch/Switch";
import { toast } from 'react-toastify';

interface UpdateAgentModalProps {
    agent: Agent | null,
    isOpen: boolean,
    openModal: () => void,
    closeModal: () => void,
    onSuccess?: () => void
}

export default function UpdateAgentModal({agent, isOpen, openModal, closeModal, onSuccess} : UpdateAgentModalProps) {
  // const { isOpen, openModal, closeModal } = useModal();
  const [selected, setSelected] = useState<string>("chatbot");
  const [description, setDescription] = useState("");
  const [moduleId ,setModuleId] = useState("");
  const [moduleList, setModuleList] = useState<any>([])
  const [code, setCode] = useState("")
  const [agentName, setAgentName] = useState("")
  const [status, setStatus] = useState("public")
  const [difyKey, setDifyKey] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const fetchModuleList = async () => {
      try{
        const data = await moduleApi.getModules(true,1000,0);
        if(data)
        {
          setModuleList(data.map((value) => { return {label: value.name, value: value.id}}))
        }
      }catch(err)
      {
        console.log(err)
      }
    }
    
    fetchModuleList()
  }, [])

  const handleSave = async () => {
    if (!agent) return;

    setIsLoading(true);
    try {
      const data = await agentApi.updateAgent(
        agent.id,
        Number(moduleId),
        code,
        difyKey,
        agentName,
        description,
        selected,
        status
      );

      if (data) {
        toast.success("Cập nhật agent thành công!");
        onSuccess?.();
        closeModal();
      }
    } catch (err) {
      console.log(err);
      // toast.error("Có lỗi xảy ra khi cập nhật agent!");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectModuleChange = (value: string) => {
    setModuleId(value);
  }

  const handleSwitchChange = (checked: boolean) => {
    if (checked) {
      setStatus("public");
    } else {
      setStatus("private");
    }
  }

  useEffect(() => {
    if (agent) {
      setSelected(agent.type.toLowerCase());
      setDescription(agent.description);
      setModuleId(String(agent.moduleId));
      setCode(agent.code);
      setAgentName(agent.name);
      setStatus(agent.status ? "public" : "private");
      setDifyKey(''); // Reset dify key for security
    }
  }, [agent])

  return (
    <div>
      <Modal
        isOpen={isOpen}
        onClose={closeModal}
        className="max-w-11/12 h-10/11"
      >
        <div className="flex justify-center overflow-y-auto overflow-x-hidden p-5 lg:p-10 h-full ">
          <div className="flex flex-row-reverse flex-1/2 pr-6 ">
            <form className=" ">
              <h4 className="mb-6 text-lg font-medium text-gray-800 dark:text-white/90">
                Thông tin Agent
              </h4>
              <div className="mb-6">
                <AgentCardSelector selected={selected} setSelected={setSelected}/>
              </div>
              <div className="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-2">
                <div className="col-span-1 sm:col-span-2">
                  <Label>Tên Agent</Label>
                  <Input
                    type="text"
                    placeholder="Tên Agent (ví dụ: SCS Agent...)"
                    defaultValue={agentName}
                    onChange={(e) => setAgentName(e.target.value)}
                  />
                </div>
                <div className="col-span-1">
                  <div>
                    <Label>Module</Label>
                    <div className="relative">
                      <Select
                        options={moduleList}
                        placeholder="Tên module (ví dụ SCS, RMD,...)"
                        onChange={handleSelectModuleChange}
                        className="dark:bg-dark-900"
                        defaultValue={moduleId}
                      />
                      <span className="absolute text-gray-500 -translate-y-1/2 pointer-events-none right-3 top-1/2 dark:text-gray-400">
                          <ChevronDownIcon/>
                        </span>
                    </div>
                  </div>
                </div>
                <div className="col-span-1">
                  <Label>Code</Label>
                  <Input
                    type="text"
                    placeholder="Mã số hiệu Agent (ví dụ VBNA...)"
                    defaultValue={code}
                    onChange={(e) => setCode(e.target.value)}
                  />
                </div>
                <div className="col-span-1">
                  <Label>Phạm vi truy cập</Label>
                  <Switch
                    label="Public (Mọi user đều nhìn thấy)"
                    defaultChecked={status === "public"}
                    onChange={handleSwitchChange}
                  />
                </div>
                <div className="col-span-1 sm:col-span-2">
                  <Label>Thông tin mô tả</Label>
                  <TextArea
                    value={description}
                    rows={6}
                    onChange={setDescription}
                    placeholder="Thông tin chi tiết về agent"
                    className="text-black"
                  />
                </div>
                <div className="col-span-1 sm:col-span-2">
                  <Label>Agent Key</Label>
                  <Input
                    type="password"
                    placeholder="Dify key (để trống nếu không muốn thay đổi)"
                    defaultValue={difyKey}
                    onChange={(e) => setDifyKey(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex items-center justify-end w-full gap-3 mt-6">
                <Button size="sm" variant="outline" onClick={closeModal} disabled={isLoading}>
                  Đóng
                </Button>
                <Button size="sm" onClick={handleSave} disabled={isLoading}>
                  {isLoading ? "Đang lưu..." : "Lưu thay đổi"}
                </Button>
              </div>
          </form>
          </div>
          
          <div className="h-full border-l pl-6 flex-1/2 border-l-gray-100">
              <div className="h-6 w-full 2xl:h-[100px]"></div>
              <h4 className="uppercase font-semibold">{selected}</h4>
              {
                selected == 'chatbot' ?<div>
                <p className="text-gray-500">Quickly build an LLM-based chatbot with simple configuration. You can switch to Chatflow later.
                </p>
                <Image
                    src="/agent-ai/images/agent/chatbot.png"
                    alt="404"
                    width={600}
                    height={200}
                  />
              </div>:
              <div>
                <p className="text-gray-500">
                  Workflow orchestration for multi-round complex dialogue tasks with memory capabilities.Learn more
                </p>
                <Image
                    src="/agent-ai/images/agent/workflow.png"
                    alt="404"
                    width={600}
                    height={200}
                  />
                </div>
              }
          </div>
        </div>
      </Modal>
    </div>
  );
}
