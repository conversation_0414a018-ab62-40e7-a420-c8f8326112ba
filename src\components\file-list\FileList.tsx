'use client';
import { FileItem } from '@/types';
import React, { useRef, useState } from 'react';
import { FaEllipsisV, FaEye, FaTrash } from 'react-icons/fa';
import { Dropdown } from '../ui/dropdown/Dropdown';
import { DropdownItem } from '../ui/dropdown/DropdownItem';
import { fileApi } from '@/api/fileApi';
import styles from '../agent/AgentChat.module.scss';

type FileListProps = {
  files: FileItem[],
  setFiles: React.Dispatch<React.SetStateAction<FileItem[]>>,
  deleteFile: any,
  conversationId?: number
}
const FileList: React.FC<FileListProps> = ({ files, setFiles, deleteFile, conversationId }) => {
  //   const fileInputRef = useRef<HTMLInputElement>(null);
  const [isOpen, setIsOpen] = useState(false)
  const [openIndex, setOpenIndex] = useState(-1)
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0, left: 0 });

  const onClickViewDetail = async () => {
    if(openIndex > -1)
    {
      const selectedFile = files[openIndex]
      let fileName =selectedFile.fileName
      if (fileName && conversationId)
      {
        const downloadLink = await fileApi.getSignedUrl(conversationId, fileName)
        window.open(downloadLink,  "_blank")
      }
      setOpenIndex(-1);
      setIsOpen(false);
    }
  }

  const onClickDelete = () => {
    if(openIndex > -1)
    {
      //handle delete files
      const selectedFile = files[openIndex]
      if(selectedFile)
      {
        deleteFile(selectedFile.fileName)
      }
      //end delete files
      
      setOpenIndex(-1);
      setIsOpen(false);
    }
  }
  const toggleDropdown = (e: any, index: number) => {
    e.preventDefault();
    const rect = e.currentTarget.getBoundingClientRect();
    console.log(rect)
    setDropdownPosition({
      top: rect.top + rect.height,
      right: rect.right,
      left: rect.left
    });
    setOpenIndex(index);
    setIsOpen(true);
  };

  function closeDropdown() {
    setIsOpen(false);
  }

  return (
    <div className={`bg-white rounded-xl border border-gray-200/60 hover:border-gray-300/80 transition-all duration-200 dark:bg-gray-800 dark:border-gray-700 ${styles.custom_scrollbar}`}>
      <div className="px-4 py-3 border-b border-gray-100/80 dark:border-gray-700">
        <div className="flex items-center justify-between">
          {/* <h4 className="font-medium text-gray-900 text-sm">Files đính kèm</h4> */}
          <h4 className="font-semibold text-gray-500 text-lg">Files đính kèm</h4>
          {files.length > 0 && (
            <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full font-medium dark:bg-gray-600 dark:text-gray-200">
              {files.length}
            </span>
          )}
        </div>
      </div>

      <div className="max-h-80 overflow-y-auto">
        {files.length === 0 ? (
          <div className="p-6 text-center">
            <div className="w-10 h-10 bg-gray-100 dark:bg-gray-500 rounded-lg flex items-center justify-center mx-auto mb-3">
              <svg className="w-5 h-5 text-gray-400 dark:text-gray-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-200">Chưa có file đính kèm</p>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {files.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50/80 dark:hover:bg-gray-900 transition-colors duration-200 group">
                <div className='flex items-center space-x-3 flex-1 min-w-0'>
                  <div className="w-8 h-8 bg-gray-100 dark:bg-gray-500 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-gray-500 dark:text-gray-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-gray-900 text-sm font-medium truncate dark:text-gray-200" title={file.fileName}>
                      {file.fileName}
                    </p>
                    <p className="text-gray-500 text-xs">
                      {file.lastModified && new Date(file.lastModified).toLocaleString()}
                    </p>
                  </div>
                </div>
                <button
                  className="p-2 rounded-lg hover:bg-gray-200/80 text-gray-400 hover:text-gray-600 transition-all duration-200 opacity-0 group-hover:opacity-100"
                  onClick={(e) => toggleDropdown(e, index)}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
      <Dropdown
        isOpen={isOpen}
        onClose={closeDropdown}
        className="fixed flex w-[200px] flex-col rounded-2xl border border-gray-200 bg-white p-3 shadow-theme-lg dark:border-gray-800 dark:bg-gray-dark z-9999"
        style={{
          top: dropdownPosition.top,
          left: dropdownPosition.right - 200,
        }}
      >
        <ul className="flex flex-col gap-1 py-2 dark:border-gray-800">
          <li>
            <DropdownItem
              onItemClick={onClickViewDetail}
              tag="a"
              className="flex items-center gap-3 px-3 py-2 font-medium text-gray-700 rounded-lg group text-theme-sm hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-white/5 dark:hover:text-gray-300"
            >   
            <FaEye/>         
              Xem chi tiết
            </DropdownItem>
          </li>
          <li>
            <DropdownItem
              onItemClick={onClickDelete}
              tag="a"
              className="flex items-center gap-3 px-3 py-2 font-medium text-gray-700 rounded-lg group text-theme-sm hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-white/5 dark:hover:text-gray-300"
            >
              <FaTrash/>
              Xóa
            </DropdownItem>
          </li>
        </ul>
      </Dropdown>
      {/* <div className="border-t px-4 py-3 bg-gray-50 flex items-center justify-between">
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          onChange={handleUpload}
        />
        <button
          onClick={() => fileInputRef.current?.click()}
          className="text-white bg-blue-600 hover:bg-blue-700 px-3 py-1.5 rounded text-sm font-medium shadow"
        >
          📤 Nộp file
        </button>
      </div> */}
    </div>
  );
};

export default FileList;
