import axiosClient from '@/lib/axiosClient';
import { Conversation, ChatMessage } from '@/types/index';

export const fileApi = {
  async downloadFile(fileKey: string, fileName?: string): Promise<void> {
    const response = await axiosClient.post(`/v1/storage/download?key=${encodeURIComponent(fileKey)}`,null, {
      responseType: 'blob'
    });
    const blob = new Blob([response.data]);
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName || fileKey.split('/').pop() || 'downloaded-file';
    a.click();
    window.URL.revokeObjectURL(url);
  },

  async getSignedUrl(conversationId: number, fileName: string) {
    const res = await axiosClient.get('/v1/conversation/get-download-url', { params: { conversationId: conversationId, fileName: fileName }})
    return res.data.data;
  },

  async getTicketSignedUrl(ticketId: number, id: number) {
    const res = await axiosClient.get('/v1/ticket/get-ticket-download-url', { params: { ticketId: ticketId, id: id }})
    return res.data.data;
  }
};