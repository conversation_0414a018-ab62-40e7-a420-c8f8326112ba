/* Enhanced Agent Management Page Animations */

/* Floating animation for stats cards */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Pulse animation for status indicators */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Shimmer effect for loading states */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Gradient shift animation */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Bounce in animation for cards */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(50px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-10px);
  }
  70% {
    transform: scale(0.9) translateY(0px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0px);
  }
}

/* Slide in from bottom */
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced hover effects for agent cards */
.agent-card {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.agent-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s;
}

.agent-card:hover::before {
  left: 100%;
}

/* Stats card animations - gentler effects */
.stats-card {
  animation: slideInUp 0.6s ease-out;
  transition: all 0.3s ease;
}

.stats-card:nth-child(1) {
  animation-delay: 0.1s;
}

.stats-card:nth-child(2) {
  animation-delay: 0.2s;
}

.stats-card:nth-child(3) {
  animation-delay: 0.3s;
}

.stats-card:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
}

/* Tab animation */
.tab-indicator {
  position: relative;
  overflow: hidden;
}

.tab-indicator::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.tab-indicator.active::after {
  transform: scaleX(1);
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Staggered animation for agent cards */
.agent-grid .agent-card {
  animation: bounceIn 0.6s ease-out;
}

.agent-grid .agent-card:nth-child(1) { animation-delay: 0.1s; }
.agent-grid .agent-card:nth-child(2) { animation-delay: 0.2s; }
.agent-grid .agent-card:nth-child(3) { animation-delay: 0.3s; }
.agent-grid .agent-card:nth-child(4) { animation-delay: 0.4s; }
.agent-grid .agent-card:nth-child(5) { animation-delay: 0.5s; }
.agent-grid .agent-card:nth-child(6) { animation-delay: 0.6s; }
.agent-grid .agent-card:nth-child(7) { animation-delay: 0.7s; }
.agent-grid .agent-card:nth-child(8) { animation-delay: 0.8s; }

/* Floating action button - softer appearance */
.floating-action {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  background: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
  transition: all 0.3s ease;
  animation: float 4s ease-in-out infinite;
}

.floating-action:hover {
  transform: scale(1.05) translateY(-3px);
  box-shadow: 0 12px 30px rgba(59, 130, 246, 0.25);
}

/* Search bar enhancement - subtle effect */
.search-enhanced {
  position: relative;
}

.search-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(59, 130, 246, 0.05);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.search-enhanced:focus-within::before {
  opacity: 1;
}

/* Modal entrance animation */
.modal-content {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Dropdown animation */
.dropdown-menu {
  animation: dropdownSlideIn 0.2s ease-out;
}

@keyframes dropdownSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Icon rotation on hover */
.icon-rotate {
  transition: transform 0.3s ease;
}

.icon-rotate:hover {
  transform: rotate(360deg);
}

/* Text gradient animation - softer colors */
.text-gradient-animated {
  background: linear-gradient(45deg, #3b82f6, #6366f1, #3b82f6);
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-shift 4s ease infinite;
}

/* Responsive animations */
@media (max-width: 768px) {
  .agent-card {
    animation: slideInUp 0.4s ease-out;
  }
  
  .stats-card {
    animation: slideInUp 0.4s ease-out;
  }
  
  .floating-action {
    width: 50px;
    height: 50px;
    bottom: 1rem;
    right: 1rem;
  }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
