"use client";
import React, { createContext, useContext, useState, useEffect } from "react";

type FTIBotPopupContextType = {
  isOpen: boolean;
  isMinimized: boolean;
  openPopup: () => void;
  closePopup: () => void;
  togglePopup: () => void;
  minimizePopup: () => void;
  maximizePopup: () => void;
  toggleMinimize: () => void;
};

const FTIBotPopupContext = createContext<FTIBotPopupContextType | undefined>(undefined);

export const useFTIBotPopup = () => {
  const context = useContext(FTIBotPopupContext);
  if (!context) {
    throw new Error("useFTIBotPopup must be used within a FTIBotPopupProvider");
  }
  return context;
};

export const FTIBotPopupProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);

  // Load state from localStorage on component mount
  useEffect(() => {
    const savedIsOpen = localStorage.getItem('fti_bot_popup_open');
    const savedIsMinimized = localStorage.getItem('fti_bot_popup_minimized');
    
    if (savedIsOpen === 'true') {
      setIsOpen(true);
    }
    if (savedIsMinimized === 'true') {
      setIsMinimized(true);
    }
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('fti_bot_popup_open', isOpen.toString());
  }, [isOpen]);

  useEffect(() => {
    localStorage.setItem('fti_bot_popup_minimized', isMinimized.toString());
  }, [isMinimized]);

  const openPopup = () => {
    setIsOpen(true);
    setIsMinimized(false);
  };

  const closePopup = () => {
    setIsOpen(false);
    setIsMinimized(false);
  };

  const togglePopup = () => {
    if (isOpen) {
      closePopup();
    } else {
      openPopup();
    }
  };

  const minimizePopup = () => {
    setIsMinimized(true);
  };

  const maximizePopup = () => {
    setIsMinimized(false);
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const value = {
    isOpen,
    isMinimized,
    openPopup,
    closePopup,
    togglePopup,
    minimizePopup,
    maximizePopup,
    toggleMinimize,
  };

  return (
    <FTIBotPopupContext.Provider value={value}>
      {children}
    </FTIBotPopupContext.Provider>
  );
};
