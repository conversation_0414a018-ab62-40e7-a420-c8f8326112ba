/* Approval Flow Enhanced Styles */

/* Button hover effects */
.approval-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  box-shadow: 0 4px 15px 0 rgba(59, 130, 246, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.approval-btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
}

.approval-btn-secondary {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.approval-btn-secondary:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.approval-btn-danger {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  color: #dc2626;
  border: 1px solid #fecaca;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.approval-btn-danger:hover {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border-color: #f87171;
  transform: translateY(-1px);
}

.approval-btn-success {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  color: #16a34a;
  border: 1px solid #bbf7d0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.approval-btn-success:hover {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-color: #86efac;
  transform: translateY(-1px);
}

/* Step card animations */
.approval-step-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.approval-step-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s;
}

.approval-step-card:hover::before {
  left: 100%;
}

.approval-step-card:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
}

/* Step number badge animation */
.approval-step-number {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.approval-step-card:hover .approval-step-number {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: scale(1.1);
}

/* Input field enhancements */
.approval-input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.approval-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}

/* Loading animation */
.approval-loading {
  position: relative;
  overflow: hidden;
}

.approval-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Modal enhancements */
.approval-modal-header {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-bottom: 1px solid #e0e7ff;
}

.dark .approval-modal-header{
  background: #101828;
  border-bottom: 1px solid #344054;
}

.approval-modal-footer {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
}

.dark .approval-modal-footer{
  background: #101828;
  border-top: 1px solid #344054;
}
/* Approver card enhancements */
.approval-approver-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .approval-approver-card {
  background: #1d2939;
}


/* Icon animations */
.approval-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.approval-icon:hover {
  transform: scale(1.1);
}

/* Pulse animation for important elements */
.approval-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .8;
  }
}

/* Fade in animation */
.approval-fade-in {
  animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in animation */
.approval-slide-in {
  animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive improvements */
@media (max-width: 768px) {
  .approval-step-card {
    padding: 1rem;
  }
  
  .approval-btn-primary,
  .approval-btn-secondary,
  .approval-btn-danger,
  .approval-btn-success {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
}
