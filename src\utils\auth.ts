export const redirectToKeycloakLogin = () => {
  const keycloakUrl = process.env.NEXT_PUBLIC_KEYCLOAK_URL!; // URL Keycloak
  const realm = process.env.NEXT_PUBLIC_REALM!;
  const clientId = process.env.NEXT_PUBLIC_CLIENT_ID!;
  const redirectUri = process.env.NEXT_PUBLIC_KEYCLOAK_CALLBACK!; // FE sẽ nhận code ở đây

  // const loginUrl = `${keycloakUrl}/auth/realms/${realm}/protocol/openid-connect/auth` +
  //   `?client_id=${clientId}` +
  //   `&response_type=code` +
  //   `&redirect_uri=${redirectUri}` +
  //   `&scope=openid profile`;

  const authUrl = new URL(`/auth/realms/${realm}/protocol/openid-connect/auth`, keycloakUrl);

  const params = new URLSearchParams({
    client_id: clientId,
    response_type: 'code',
    redirect_uri: redirectUri,
    scope: 'openid profile',
  });

  authUrl.search = params.toString();

  window.location.href = authUrl.toString();
};