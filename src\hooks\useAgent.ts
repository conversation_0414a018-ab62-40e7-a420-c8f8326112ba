import { useEffect, useState } from 'react';
import { Agent } from '@/types';
import { agentApi } from '@/api/agentApi';

export function useAgent(agentCode?: string) {
  const [agent, setAgent] = useState<Agent | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchAgent = async (agentCode: string) => {
    const agent = await agentApi.getAgentByCode(agentCode)
    if (agent == null)
      setAgent(null)
    else
      setAgent(agent)
  }

  useEffect(() => {
    if (!agentCode) return;
    setLoading(true);
    fetchAgent(agentCode)
    
  }, [agentCode]);

  return { agent, loading };
}