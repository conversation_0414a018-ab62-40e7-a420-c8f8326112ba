FROM node:22.11.0 AS deps

WORKDIR /app

COPY package*.json ./
RUN npm install

FROM node:22.11.0 AS builder

WORKDIR /app
ARG ENVIRONMENT_CONFIG

COPY . .
COPY --from=deps /app/node_modules ./node_modules
COPY .env.${ENVIRONMENT_CONFIG} ./.env

RUN npm run build


FROM node:22.11.0 AS runner

WORKDIR /app

ENV NODE_ENV=production

COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/next.config.ts ./next.config.ts

EXPOSE 3000

CMD ["npm", "start"]
