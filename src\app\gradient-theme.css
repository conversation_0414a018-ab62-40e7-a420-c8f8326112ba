/* Enhanced Blue Ocean Single Color Theme System */

:root {
  --gradient-primary: #3b82f6;
  --gradient-primary-hover: #2940B8;
  --gradient-primary-light: #F0F2FF;
  --gradient-primary-subtle: rgba(55, 82, 216, 0.1);
  --gradient-secondary: #3b82f6;
  --gradient-secondary-hover: #2940B8;
  --gradient-animated: #3752D8;
  --gradient-animated-size: 200% 200%;
  --gradient-success: #10b981;
  --gradient-warning: #f59e0b;
  --gradient-error: #ef4444;
}

/* Additional Gradient Animations */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes gradient-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes gradient-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(55, 82, 216, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(55, 82, 216, 0.5);
  }
}

/* Enhanced Gradient Components */
.gradient-button {
  background: #3752D8;
  transition: all 0.3s ease;
  border: none;
  color: white;
  font-weight: 500;
  border-radius: 12px;
  padding: 12px 24px;
  cursor: pointer;
}

.gradient-button:hover {
  background: #2940B8;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(55, 82, 216, 0.3);
}

.gradient-button:active {
  background: #1E2A8A;
  transform: translateY(0);
}

/* Gradient Cards */
.gradient-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  border: 1px solid transparent;
  background-clip: padding-box;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
}

.gradient-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #3752D8;
  z-index: -1;
  margin: -1px;
  border-radius: inherit;
}

.gradient-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(55, 82, 216, 0.2);
}

/* Gradient Text Effects */
.gradient-text-animated {
  color: #3752D8;
  font-weight: 700;
}

.gradient-text-glow {
  color: #3752D8;
  filter: drop-shadow(0 0 10px rgba(55, 82, 216, 0.5));
}

/* Gradient Borders */
.gradient-border {
  border: 2px solid #3752D8;
  border-radius: 8px;
}

.gradient-border-animated {
  border: 2px solid #3752D8;
  border-radius: 8px;
  animation: border-pulse 2s ease infinite;
}

@keyframes border-pulse {
  0%, 100% { border-color: #3752D8; }
  50% { border-color: #2940B8; }
}

/* Gradient Backgrounds */
.gradient-bg-subtle {
  background: var(--gradient-primary-subtle);
}

.gradient-bg-light {
  background: var(--gradient-primary-light);
}

.gradient-bg-animated {
  background: #3752D8;
}

/* Gradient Overlays */
.gradient-overlay {
  position: relative;
}

.gradient-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(55, 82, 216, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.gradient-overlay:hover::before {
  opacity: 1;
}

/* Gradient Shadows */
.gradient-shadow {
  box-shadow: 0 10px 30px rgba(55, 82, 216, 0.2);
}

.gradient-shadow-animated {
  animation: gradient-glow 3s ease infinite;
}

/* Gradient Progress Bars */
.gradient-progress {
  background: #3752D8;
  height: 8px;
  border-radius: 4px;
}

/* Gradient Loading Spinner */
.gradient-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid transparent;
  border-top: 4px solid #3752D8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Gradient Navigation */
.gradient-nav-item {
  position: relative;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.gradient-nav-item.active {
  background: #F0F2FF;
  color: #3752D8;
}

.gradient-nav-item:hover {
  background: rgba(55, 82, 216, 0.1);
}

/* Gradient Form Elements */
.gradient-input:focus {
  outline: none;
  border: 2px solid #3752D8;
  box-shadow: 0 0 0 3px rgba(55, 82, 216, 0.1);
}

.gradient-checkbox:checked {
  background: #3752D8;
  border-color: transparent;
}

/* Gradient Status Indicators */
.gradient-status-success {
  background: #10b981;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.gradient-status-warning {
  background: #f59e0b;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.gradient-status-error {
  background: #ef4444;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

/* Gradient Pagination */
.gradient-pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.gradient-pagination-button {
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  background: white;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.gradient-pagination-button:hover {
  background: #F0F2FF;
  border-color: #3752D8;
  color: #3752D8;
  transform: translateY(-1px);
}

.gradient-pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.gradient-pagination-button.active {
  background: #3752D8;
  border-color: transparent;
  color: white;
  box-shadow: 0 4px 12px rgba(55, 82, 216, 0.3);
}

.gradient-pagination-button.active:hover {
  background: #2940B8;
}

.gradient-pagination-ellipsis {
  padding: 8px 4px;
  color: #9ca3af;
  font-size: 14px;
}

/* Gradient Menu Bar */
.gradient-menu-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(55, 82, 216, 0.1);
}

.gradient-menu-item {
  position: relative;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: #374151;
  font-weight: 500;
  text-decoration: none;
}

.gradient-menu-item:hover {
  background: #F0F2FF;
  color: #3752D8;
  transform: translateY(-1px);
}

.gradient-menu-item.active {
  background: #F0F2FF;
  color: #3752D8;
  position: relative;
}

.gradient-menu-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 3px;
  background: #3752D8;
  border-radius: 2px;
}

/* Gradient Search Bar */
.gradient-search-bar {
  position: relative;
  display: flex;
  align-items: center;
}

.gradient-search-input {
  padding: 10px 16px 10px 40px;
  /* border: 2px solid #e5e7eb; */
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  width: 100%;
}

.gradient-search-input:focus {
  outline: none;
  border: 2px solid #3752D8;
  box-shadow: 0 0 0 3px rgba(55, 82, 216, 0.1);
}

.gradient-search-icon {
  position: absolute;
  left: 12px;
  color: #9ca3af;
  transition: color 0.3s ease;
}

.gradient-search-input:focus + .gradient-search-icon {
  color: #3752D8;
}

/* Responsive Gradient Adjustments */
@media (max-width: 768px) {
  .gradient-button {
    padding: 10px 20px;
    font-size: 14px;
  }
  
  .gradient-card {
    border-radius: 8px;
  }
  
  .gradient-text-animated {
    font-size: 18px;
  }
}

/* Dark Mode Gradient Adjustments */
@media (prefers-color-scheme: dark) {
  .gradient-card {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.9) 0%, rgba(31, 41, 55, 0.7) 100%);
  }
  
  .gradient-input:focus {
    border: 2px solid #3752D8;
    box-shadow: 0 0 0 3px rgba(55, 82, 216, 0.1);
  }
}
