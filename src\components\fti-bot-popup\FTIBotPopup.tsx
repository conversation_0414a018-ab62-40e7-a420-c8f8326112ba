"use client";

import React, { useEffect, useState, useRef } from "react";
import { useFTIBotPopup } from "@/context/FTIBotPopupContext";
import { conversationApi } from "@/api/conversationApi";
import { ChatMessage } from "@/types";
import MarkdownViewer from "@/components/markdownviewer/MarkdownViewer";
import {
  FaRobot,
  FaPaperPlane,
  FaSync,
  FaTimes,
  FaMinus,
  FaExpand,
  FaCompress
} from "react-icons/fa";

export default function FTIBotPopup() {
  const { isOpen, isMinimized, closePopup, toggleMinimize } = useFTIBotPopup();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [conversationId, setConversationId] = useState(0);
  const [hasMore, setHasMore] = useState<boolean>(false);
  const [chatLoading, setChatLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const chatRef = useRef<HTMLDivElement>(null);
  const loadingMore = useRef(false);
  const agentCode = process.env.NEXT_PUBLIC_AGENT_CODE_DASHBOARD || "TICKETSUPPORT";

  // Load conversationId from localStorage on component mount
  useEffect(() => {
    const savedConversationId = localStorage.getItem('fti_bot_popup_conversation_id');
    if (savedConversationId) {
      setConversationId(parseInt(savedConversationId, 10));
    }
  }, []);

  // Save conversationId to localStorage whenever it changes
  useEffect(() => {
    if (conversationId > 0) {
      localStorage.setItem('fti_bot_popup_conversation_id', conversationId.toString());
    }
  }, [conversationId]);

  // Fetch chat history when conversationId changes
  useEffect(() => {
    const fetchHistory = async () => {
      if (conversationId <= 0) return;

      setChatLoading(true);
      try {
        const messageData = await conversationApi.getChatHistory(conversationId, 20, "");
        if (messageData != null && messageData.data != null && messageData.data.length > 0) {
          setMessages(messageData.data);
          setHasMore(messageData.hasMore);
        }
      } catch (err) {
        console.error("Error fetching chat history:", err);
      } finally {
        setChatLoading(false);
      }
    };

    fetchHistory();
  }, [conversationId]);

  useEffect(() => {
    if (!loadingMore.current) {
      chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight, behavior: 'smooth' });
    }
    loadingMore.current = false;
  }, [messages]);

  // Handle scroll for load more functionality
  useEffect(() => {
    const el = chatRef.current;
    if (!el || chatLoading) return;

    const handleScroll = async () => {
      if (el.scrollTop === 0 && hasMore) {
        loadingMore.current = true;

        const oldScrollHeight = el.scrollHeight;
        await loadMore();

        requestAnimationFrame(() => {
          const newScrollHeight = el.scrollHeight;
          el.scrollTop = newScrollHeight - oldScrollHeight;
        });
      }
    };

    el.addEventListener('scroll', handleScroll);
    return () => el.removeEventListener('scroll', handleScroll);
  }, [chatLoading, hasMore]);

  const handleSend = async () => {
    if (!input.trim()) return;

    const inputValue = input;
    setMessages(prev => [...prev, {
        "query": inputValue,
        "answer": "(thinking)",
        "conversationId": 0,
        "createTicket": false,
        "id": ""
      }]);
    setInput("");

    try {
      const data = await conversationApi.sendMessage(inputValue, conversationId, [], agentCode, {});
      if (data != null) {
        if (data.conversationId) {
          setConversationId(data.conversationId);
        }
        setMessages(prevMessages => {
          const lastIndex = prevMessages.length - 1;
          const updatedMessages = [...prevMessages];
          updatedMessages[lastIndex].answer = data.answer ?? "Hệ thống đang gặp sự cố phản hồi chậm...";
          return updatedMessages;
        });
      }
    } catch (err) {
      console.error("Error sending message:", err);
    }
  };

  const handleRefreshConversation = () => {
    setMessages([]);
    setConversationId(0);
    setHasMore(false);
    localStorage.removeItem('fti_bot_popup_conversation_id');
  };

  const loadMore = async () => {
    if (!hasMore || messages.length === 0) return;

    const firstId = messages[0].id;
    try {
      const messageData = await conversationApi.getChatHistory(conversationId, 20, firstId);
      if (messageData != null && messageData.data != null && messageData.data.length > 0) {
        setMessages(prev => [...messageData.data, ...prev]);
        setHasMore(messageData.hasMore);
      }
    } catch (err) {
      console.error("Error loading more messages:", err);
    }
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  if (!isOpen) return null;

  // Determine popup size based on state
  const popupWidth = isExpanded ? 'w-[600px]' : 'w-[400px]';
  const popupHeight = isExpanded ? 'h-[700px]' : 'h-[500px]';
  const minimizedHeight = isMinimized ? 'h-[75px]' : popupHeight;

  return (
    <div className={`fixed bottom-6 right-6 ${popupWidth} ${minimizedHeight} bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-600 shadow-2xl flex flex-col z-[9999] transition-all duration-300 ease-in-out`}>
      {/* Header */}
      <div className={`p-4 ${!isMinimized ? 'border-b border-gray-200 dark:border-gray-600' : 'rouded-b-xl'} dark:border-gray-600 bg-gradient-to-r from-sky-50/10 to-blue-50/5 rounded-t-xl`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
              <FaRobot className="text-white text-lg" />
            </div>
            <div>
              <h2 className="text-base font-semibold text-sky-700 dark:text-gray-100">
                FTI Bot
              </h2>
              <div className="flex items-center text-xs text-sky-600 dark:text-gray-100">
                <span className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1.5"></span>
                <span>Đang trực tuyến</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            {!isMinimized && (
              <>
                <button
                  onClick={toggleExpanded}
                  className="p-1.5 text-sky-600 hover:text-sky-700 hover:bg-sky-50 rounded-lg transition-colors duration-200"
                  title={isExpanded ? "Thu nhỏ" : "Mở rộng"}
                >
                  {isExpanded ? <FaCompress className="text-sm" /> : <FaExpand className="text-sm" />}
                </button>
                <button
                  onClick={handleRefreshConversation}
                  className="p-1.5 text-sky-600 hover:text-sky-700 hover:bg-sky-50 rounded-lg transition-colors duration-200"
                  title="Làm mới cuộc trò chuyện"
                >
                  <FaSync className="text-sm" />
                </button>
              </>
            )}
            <button
              onClick={toggleMinimize}
              className="p-1.5 text-sky-600 hover:text-sky-700 hover:bg-sky-50 rounded-lg transition-colors duration-200"
              title={isMinimized ? "Mở rộng" : "Thu gọn"}
            >
              <FaMinus className="text-sm" />
            </button>
            <button
              onClick={closePopup}
              className="p-1.5 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200"
              title="Đóng"
            >
              <FaTimes className="text-sm" />
            </button>
          </div>
        </div>
      </div>

      {/* Chat Content - Hidden when minimized */}
      {!isMinimized && (
        <>
          {/* Chat Messages */}
          <div className="flex-1 overflow-y-auto p-3 space-y-3" ref={chatRef}>
            {/* Loading indicator for initial chat history */}
            {chatLoading && messages.length === 0 && (
              <div className="text-center text-gray-500 py-6">
                <div className="flex items-center justify-center space-x-1 mb-3">
                  <div className="w-2 h-2 bg-sky-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                  <div className="w-2 h-2 bg-sky-600 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                </div>
                <p className="text-xs">Đang tải lịch sử trò chuyện...</p>
              </div>
            )}

            {/* Load more indicator at the top */}
            {hasMore && messages.length > 0 && (
              <div className="text-center py-2">
                <div className="text-xs text-gray-400">Cuộn lên để tải thêm tin nhắn cũ</div>
              </div>
            )}

            {messages.length === 0 && !chatLoading && (
              <div className="text-center text-gray-500 py-6">
                <FaRobot className="text-3xl text-gray-300 mx-auto mb-3" />
                <p className="text-xs">Xin chào! Tôi có thể giúp gì cho bạn? 👋</p>
              </div>
            )}

            {messages.map((message, index) => (
              <div key={index} className="space-y-2">
                {/* User message */}
                <div className="flex justify-end">
                  <div className="bg-gradient-primary text-white rounded-2xl rounded-br-md px-3 py-2 max-w-[80%] shadow-sm">
                    <p className="text-xs">{message.query}</p>
                  </div>
                </div>

                {/* AI response */}
                {message.answer && message.answer !== '(thinking)' && (
                  <div className="flex">
                    <div className="bg-gradient-to-r from-sky-50 to-blue-50 rounded-2xl rounded-bl-md px-3 py-2 max-w-[80%]">
                      <MarkdownViewer content={message.answer} className="dark:text-gray-900 text-xs" />
                    </div>
                  </div>
                )}

                {message.answer === '(thinking)' && (
                  <div className="flex">
                    <div className="bg-gradient-to-r from-sky-50 to-blue-50 rounded-2xl rounded-bl-md px-3 py-2 shadow-sm">
                      <div className="flex items-center space-x-1">
                        <div className="w-1.5 h-1.5 bg-sky-400 rounded-full animate-bounce"></div>
                        <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-1.5 h-1.5 bg-sky-600 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Input */}
          <div className="p-3 border-t border-gray-200 dark:border-gray-600 bg-gradient-to-r from-sky-50/10 to-blue-50/5 rounded-b-xl">
            <div className="flex space-x-2">
              <div className="relative flex-1">
                <input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSend()}
                  placeholder="Nhập tin nhắn..."
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-xs focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-all duration-300 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md"
                />
              </div>
              <button
                onClick={handleSend}
                className="bg-gradient-primary hover:bg-gradient-primary-hover text-white px-3 py-2 rounded-lg text-xs flex items-center hover:shadow-lg hover:scale-105 transition-all duration-300"
              >
                <FaPaperPlane className="transform hover:translate-x-0.5 transition-transform duration-300" />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
