import axiosClient from '@/lib/axiosClient';
import { Conversation, ChatMessage, Agent, ParamItem } from '@/types/index';

export const agentApi = {
  async getAgents(limit: number = 10, offset: number = 0): Promise<Agent[]> {
    const res = await axiosClient.get('/v1/agent/get-agents', {params: {limit: limit, offset: offset}});
    return res.data.data;
  },
  async getAgentByCode(code: string, signal?: AbortSignal): Promise<Agent | null> {
    const res = await axiosClient.get('/v1/agent/get-agent-by-code', { params: { code: code }, signal });
    return res.data.data;
  },
  
  async getDifyParameters(agentCode: string, signal?: AbortSignal): Promise<ParamItem[]> {
    const res = await axiosClient.get(`/v1/agent/get-agent-parameters`, { params: { agentCode: agentCode }, signal });
    return res.data.data;
  },
  async addAgent(moduleId: number, code: string, difyKey: string, name: string, description: string, type: string, status: string, signal?: AbortSignal): Promise<boolean> {
    const res = await axiosClient.post('/v1/agent/add-agent', 
      JSON.stringify({
        moduleId: moduleId,
        code: code,
        difyKey: difyKey,
        name: name,
        description: description,
        type: type,
        status: status
      }), {signal}
    );
    return res.data.data;
  },

  async updateAgent(id: number, moduleId: number, code: string, difyKey: string, name: string, description: string, type: string, status: string, signal?: AbortSignal): Promise<boolean> {
    const res = await axiosClient.post('/v1/agent/update-agent',
      JSON.stringify({
        id: id,
        moduleId: moduleId,
        code: code,
        difyKey: difyKey,
        name: name,
        description: description,
        type: type,
        status: status
      }), {signal}
    );
    return res.data.data;
  },

  async deleteAgent(id: number, signal?: AbortSignal): Promise<boolean> {
    const res = await axiosClient.post('/v1/agent/delete-agent',
      JSON.stringify({
        id: id
      }), {signal}
    );
    return res.data.data;
  }
};