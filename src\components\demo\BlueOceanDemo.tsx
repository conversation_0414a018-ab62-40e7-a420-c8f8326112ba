'use client';

import React, { useState } from 'react';
import { FaWater, FaShip, FaAnchor, FaCompass } from 'react-icons/fa';
import Button from '../ui/button/Button';
import SearchBar from '../ui/search/SearchBar';
import GradientSelect from '../ui/select/GradientSelect';

const BlueOceanDemo: React.FC = () => {
  const [searchValue, setSearchValue] = useState('');
  const [selectValue, setSelectValue] = useState('');

  return (
    <div className="p-8 space-y-8 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gradient-animated mb-4">
            🌊 Blue Ocean Theme
          </h1>
          <p className="text-lg text-gray-600">
            Chủ đạo màu xanh nước biển với gradient nhẹ và đẹp
          </p>
        </div>

        {/* Color Palette */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Color Palette</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="w-20 h-20 bg-brand-400 rounded-lg mx-auto mb-2 shadow-lg"></div>
              <p className="text-sm font-medium">Brand 400</p>
              <p className="text-xs text-gray-500">#7592ff</p>
            </div>
            <div className="text-center">
              <div className="w-20 h-20 bg-blue-500 rounded-lg mx-auto mb-2 shadow-lg"></div>
              <p className="text-sm font-medium">Blue 500</p>
              <p className="text-xs text-gray-500">#3b82f6</p>
            </div>
            <div className="text-center">
              <div className="w-20 h-20 bg-blue-700 rounded-lg mx-auto mb-2 shadow-lg"></div>
              <p className="text-sm font-medium">Blue 700</p>
              <p className="text-xs text-gray-500">#1e40af</p>
            </div>
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-primary rounded-lg mx-auto mb-2 shadow-lg"></div>
              <p className="text-sm font-medium">Gradient</p>
              <p className="text-xs text-gray-500">Ocean Blue</p>
            </div>
          </div>
        </div>

        {/* Buttons Section */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Ocean Blue Buttons</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="primary" startIcon={<FaWater />}>
              Primary Ocean
            </Button>
            <Button variant="outline" startIcon={<FaShip />}>
              Outline
            </Button>
            <button className="bg-gradient-secondary text-white px-6 py-3 rounded-lg font-semibold hover:bg-gradient-secondary-hover transition-all duration-300">
              <FaAnchor className="inline mr-2" />
              Secondary
            </button>
            <button className="bg-gradient-animated text-white px-6 py-3 rounded-lg font-semibold">
              <FaCompass className="inline mr-2" />
              Animated
            </button>
          </div>
        </div>

        {/* Cards Section */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Ocean Cards</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="group p-6 bg-white border border-gray-100 hover:border-brand-300 rounded-xl shadow-sm cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-brand-100">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg shadow-brand-200">
                  <FaWater className="text-white text-2xl" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 group-hover:text-brand-700 transition-colors duration-300 mb-2">Deep Ocean</h3>
                <p className="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Sâu thẳm như đại dương xanh</p>
              </div>
            </div>
            
            <div className="group p-6 bg-white border border-gray-100 hover:border-brand-300 rounded-xl shadow-sm cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-brand-100">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg shadow-brand-200">
                  <FaShip className="text-white text-2xl" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 group-hover:text-brand-700 transition-colors duration-300 mb-2">Ocean Voyage</h3>
                <p className="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Hành trình trên biển xanh</p>
              </div>
            </div>
            
            <div className="group p-6 bg-white border border-gray-100 hover:border-sky-300 rounded-xl shadow-sm cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-sky-100">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-animated rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg shadow-sky-200">
                  <FaCompass className="text-white text-2xl" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 group-hover:text-sky-700 transition-colors duration-300 mb-2">Navigation</h3>
                <p className="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Định hướng trên đại dương</p>
              </div>
            </div>
          </div>
        </div>

        {/* Form Elements */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Ocean Form Elements</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-3">Ocean Search</h3>
              <SearchBar
                value={searchValue}
                onChange={setSearchValue}
                placeholder="Tìm kiếm trong đại dương..."
              />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-3">Ocean Select</h3>
              <GradientSelect
                value={selectValue}
                onChange={setSelectValue}
                options={[
                  { value: 'pacific', label: 'Thái Bình Dương' },
                  { value: 'atlantic', label: 'Đại Tây Dương' },
                  { value: 'indian', label: 'Ấn Độ Dương' }
                ]}
                placeholder="Chọn đại dương"
              />
            </div>
          </div>
        </div>

        {/* Text Effects */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Ocean Text Effects</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-3xl font-bold text-gradient-primary">Ocean Blue Gradient</h3>
            </div>
            <div>
              <h3 className="text-3xl font-bold text-gradient-animated">Flowing Ocean Waves</h3>
            </div>
            <div>
              <h3 className="text-3xl font-bold text-brand-600">Deep Sea Blue</h3>
            </div>
          </div>
        </div>

        {/* Status Indicators */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Ocean Status</h2>
          <div className="flex flex-wrap gap-4">
            <span className="bg-brand-100 text-brand-800 px-3 py-1 rounded-full text-sm font-medium">
              🌊 Calm Waters
            </span>
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
              ⛵ Sailing
            </span>
            <span className="bg-gradient-primary text-white px-3 py-1 rounded-full text-sm font-medium">
              🚢 Ocean Voyage
            </span>
          </div>
        </div>

        {/* Progress Bars */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Ocean Progress</h2>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Ocean Exploration</span>
                <span className="text-sm text-sky-600">75%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div className="gradient-progress h-3 rounded-full" style={{width: '75%'}}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Deep Sea Discovery</span>
                <span className="text-sm text-sky-600">60%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div className="gradient-progress h-3 rounded-full" style={{width: '60%'}}></div>
              </div>
            </div>
          </div>
        </div>

        {/* Module Card Example */}
        <div className="bg-white rounded-xl p-6 shadow-lg">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Module Card Example</h2>
          <div className="group p-5 bg-white border border-gray-100 hover:border-brand-300 rounded-xl shadow-sm cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-brand-100 max-w-sm">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <div className="w-12 h-12 rounded-xl flex items-center justify-center mr-3 bg-gradient-primary shadow-lg shadow-brand-200">
                  <FaWater className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="font-bold text-gray-800 group-hover:text-brand-700 transition-colors duration-300">Ocean Module</h3>
                  <span className="text-xs px-2 py-1 rounded-full bg-sky-100 text-sky-800">
                    Đang hoạt động
                  </span>
                </div>
              </div>
            </div>
            <p className="text-gray-600 text-sm group-hover:text-gray-700 transition-colors duration-300">
              Module quản lý đại dương với gradient xanh nước biển đẹp mắt
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlueOceanDemo;
