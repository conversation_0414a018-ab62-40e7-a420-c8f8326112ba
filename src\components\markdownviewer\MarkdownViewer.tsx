// components/MarkdownViewer.tsx

'use client'

import React from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkBreaks from 'remark-breaks'

interface Props {
  content: string,
  className?: string
}

const MarkdownViewer: React.FC<Props> = ({ content, className = "" }) => {
  return (
    <div className={`prose prose-sm max-w-none dark:prose-invert leading-relaxed ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkBreaks]}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}

export default MarkdownViewer
