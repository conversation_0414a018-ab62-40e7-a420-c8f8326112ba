import type { NextConfig } from "next";

require('global-agent/bootstrap');

const nextConfig: NextConfig = {
  basePath: '/agent-ai',
  assetPrefix: '/agent-ai',
  trailingSlash: true,
  output: "standalone",
  generateBuildId: async () => {
    // This could be anything, using the latest git hash
    return process.env.GIT_HASH || 'default-build-id';
  },
  // distDir: "output",
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });
    return config;
  },
   
};


export default nextConfig;