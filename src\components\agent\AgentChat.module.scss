.chat_message_ai {
    // background: #228EDB26;
    border-radius: 18px 18px 18px 4px;
}
.chat_message_user {
    // background-color: #E7EAEBBD;
    background-color: #465FFF;
    color: white;
    border-radius: 18px 18px 4px 18px;
}
.custom_scrollbar::-webkit-scrollbar {
    width: 6px;
}
.custom_scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
}
.custom_scrollbar::-webkit-scrollbar-thumb {
    background: #3752D8;
    border-radius: 3px;
}
.gradient_bg {
    background: #3752D8;
}
.service_card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(55, 82, 216, 0.15);
}

// Sidebar animations and styles
.sidebar_container {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar_overlay {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.info_button {
    position: relative;
    overflow: hidden;
}

.info_button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.info_button:hover::before {
    width: 100%;
    height: 100%;
}

// Enhanced card styles - Workplace inspired
.ticket_card, .file_card {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        border-color: rgba(156, 163, 175, 0.4);
    }
}

// Modern sidebar styling
.sidebar_modern {
    backdrop-filter: blur(20px);
    background: rgba(249, 250, 251, 0.8);
    border-left: 1px solid rgba(229, 231, 235, 0.6);
}

// Smooth animations
.fade_in {
    animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// Modern button hover effects
.modern_button {
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    &:hover::before {
        left: 100%;
    }
}

// Responsive design
@media (max-width: 768px) {
    .ticket_card, .file_card {
        margin: 0 -0.5rem;
        border-radius: 0.75rem;
    }

    .sidebar_modern {
        width: 100vw !important;
        height: 100vh !important;
    }
}

// Smooth scrollbar
.custom_scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;

    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(156, 163, 175, 0.5);
        border-radius: 3px;

        &:hover {
            background: rgba(156, 163, 175, 0.7);
        }
    }
}

