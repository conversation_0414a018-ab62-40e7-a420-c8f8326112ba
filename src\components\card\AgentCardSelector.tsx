'use client';

import { useState } from 'react';
import { FaRobot, FaMagic } from 'react-icons/fa';

interface AgentCardSelectorProps {
    selected: string,
    setSelected: any
}

export default function AgentCardSelector({selected, setSelected} : AgentCardSelectorProps) {

  const cards = [
    {
      key: 'chatbot',
      icon: <FaRobot className="text-white text-lg" />,
      title: 'Chatbot',
      desc: 'Sử dụng AI cho nghiệp vụ hỏi đáp, tra cứu thông tin',
      color: 'bg-sky-500',
      border: 'border-blue-500'
    },
    {
      key: 'workflow',
      icon: <FaMagic className="text-white text-lg" />,
      title: 'Workflow',
      desc: 'Sử dụng AI cho luồng nghiệp vụ phức tạp',
      color: 'bg-blue-500',
      border: 'border-blue-500'
    }
  ];

  return (
    <div className="flex space-x-4">
      {cards.map(card => (
        <div
          key={card.key}
          className={`cursor-pointer w-64 rounded-xl p-4 border transition-shadow hover:shadow-lg ${
            selected === card.key ? `${card.border} shadow-md` : 'border-gray-200'
          }`}
          onClick={() => setSelected(card.key as 'chatbot' | 'workflow')}
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${card.color}`}>
              {card.icon}
            </div>
            <h2 className="text-base font-semibold dark:text-gray-200">{card.title}</h2>
          </div>
          <p className="text-md text-gray-600 dark:text-gray-400">{card.desc}</p>
        </div>
      ))}
    </div>
  );
}
