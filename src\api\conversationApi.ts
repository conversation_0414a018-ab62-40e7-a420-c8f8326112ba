import axiosClient from '@/lib/axiosClient';
import { Conversation, ChatMessage, ConversationParamItem, FileItem, ChatHistory } from '@/types/index';

export const conversationApi = {
  async getConversations(agent: number, signal?: AbortSignal): Promise<Conversation[]> {
    const res = await axiosClient.get('/v1/conversation/get-conversations', { params: { agentid: agent }, signal });
    return res.data.data;
  },
   async getConversationsByTicket(ticketId: number, signal?: AbortSignal): Promise<Conversation[]> {
    const res = await axiosClient.get('/v1/conversation/get-conversations', { params: { ticketId: ticketId }, signal });
    return res.data.data;
  },
  async getConversationsDetail(conversationId: number, signal?: AbortSignal): Promise<Conversation | null>{
    const res = await axiosClient.get('/v1/conversation/get-conversations', { params: { conversationId: conversationId }, signal });
    return res.data.data != null ? res.data.data[0] : null;
  },

  async getChatHistory(conversationId: number, limit?: number, firstId?: string, signal?: AbortSignal): Promise<ChatHistory> {
    const res = await axiosClient.get(`/v1/conversation/get-chat-history`, { params: { conversationId: conversationId, limit: limit, firstId: firstId }, signal });
    return res.data.data;

  },

  async sendMessage(query: string, conversationId: number, files: File[], agentCode: string, input: Record<string, string>, signal?: AbortSignal): Promise<ChatMessage> {
    const formData = new FormData();

    formData.append('message', query);
    formData.append('conversationId', conversationId.toString());
    formData.append('agentCode', agentCode);
    formData.append('input', JSON.stringify(input))

    console.log(input)
    files.forEach(file => {
      formData.append('Files', file);
    });

    const res = await axiosClient.post('/v1/conversation/send-message', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      signal
    });

    return res.data.data;
  },

  async getConversationsPagination(agent: number, limit: number = 10, offset: number = 0, search?: string, signal?: AbortSignal): Promise<Conversation[]> {
    const res = await axiosClient.get('/v1/conversation/get-conversations', {
      params: { agentid: agent, limit: limit, offset: offset, topic: search },
      signal
    });
    return res.data.data;
  },

  async updateConversastion(conversationId: number, topic?: string): Promise<boolean> {
    const res = await axiosClient.post(`/v1/conversation/update-conversation`, JSON.stringify(
      { id: conversationId ,topic: topic}
    ));
    return res.data.data;
  },

  async deleteConversation(conversationId: number): Promise<boolean> {
    const res = await axiosClient.post(`/v1/conversation/delete-conversation`, null, {
      params: { conversationId: conversationId }
    });
    return res.data.data;
  },
  
  async getConversationParameters(conversationId: number,  signal?: AbortSignal ): Promise<ConversationParamItem[]> {
    const res = await axiosClient.get('/v1/conversation/get-conversation-parameters', { params: {conversationId : conversationId}, signal});
    return res.data.data;
  },

  async getFilesInConversation(conversationId: number, signal?: AbortSignal): Promise<FileItem[]> {
    const res = await axiosClient.get('/v1/conversation/get-files-conversation', { params: { conversationId: conversationId }, signal})
    return res.data.data;
  },

  async deleteFileInConversation(conversationId: number, fileName: string, signal?: AbortSignal) : Promise<boolean>{
    const res = await axiosClient.post('/v1/conversation/delete-file-conversation',null, { params: { ConversationId: conversationId, FileName: fileName }, signal})
    return res.data.data;
  },

  async createTicket(conversationId: number, signal? : AbortSignal): Promise<number> {
    const res = await axiosClient.post('/v1/conversation/generate-ticket', null, { params: { conversationId: conversationId }, signal})
    return res.data.data;
  },

  async uploadFile(conversationId: number, files: File[], signal? : AbortSignal): Promise<boolean>{
     const formData = new FormData();

      formData.append('conversationId', conversationId.toString());

      files.forEach(file => {
        formData.append('Files', file);
      });

      const res = await axiosClient.post('/v1/conversation/upload-file', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        signal
      });

      return res.data.data;
  } 
  
};