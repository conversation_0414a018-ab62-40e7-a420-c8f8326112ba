'use client';

import React, { useState } from 'react';
import { FaRocket, FaStar, FaHeart, FaBolt } from 'react-icons/fa';
import Pagination from '../ui/pagination/Pagination';
import SearchBar from '../ui/search/SearchBar';
import GradientSelect from '../ui/select/GradientSelect';

const GradientDemo: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchValue, setSearchValue] = useState('');
  const [selectValue, setSelectValue] = useState('');

  return (
    <div className="p-8 space-y-8 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gradient-animated mb-4">
            🎨 Gradient Theme System
          </h1>
          <p className="text-lg text-gray-600">
            Hệ thống gradient đẹp mắt và hiện đại cho ứng dụng
          </p>
        </div>

        {/* Buttons Section */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Gradient Buttons</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="gradient-button">
              <FaRocket className="inline mr-2" />
              Primary Button
            </button>
            <button className="bg-gradient-secondary text-white px-6 py-3 rounded-lg font-semibold hover:bg-gradient-secondary-hover transition-all duration-300">
              <FaStar className="inline mr-2" />
              Secondary
            </button>
            <button className="bg-gradient-accent text-white px-6 py-3 rounded-lg font-semibold hover:bg-gradient-accent-hover transition-all duration-300">
              <FaHeart className="inline mr-2" />
              Accent
            </button>
            <button className="bg-gradient-animated text-white px-6 py-3 rounded-lg font-semibold">
              <FaBolt className="inline mr-2" />
              Animated
            </button>
          </div>
        </div>

        {/* Cards Section */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Gradient Cards</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="gradient-card p-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaRocket className="text-white text-2xl" />
                </div>
                <h3 className="text-xl font-semibold text-gradient-primary mb-2">Performance</h3>
                <p className="text-gray-600">Tối ưu hiệu suất với gradient đẹp mắt</p>
              </div>
            </div>
            
            <div className="gradient-card p-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaStar className="text-white text-2xl" />
                </div>
                <h3 className="text-xl font-semibold text-gradient-primary mb-2">Quality</h3>
                <p className="text-gray-600">Chất lượng cao với thiết kế hiện đại</p>
              </div>
            </div>
            
            <div className="gradient-card p-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaHeart className="text-white text-2xl" />
                </div>
                <h3 className="text-xl font-semibold text-gradient-primary mb-2">Experience</h3>
                <p className="text-gray-600">Trải nghiệm người dùng tuyệt vời</p>
              </div>
            </div>
          </div>
        </div>

        {/* Text Effects Section */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Text Effects</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-3xl font-bold text-gradient-primary">Gradient Primary Text</h3>
            </div>
            <div>
              <h3 className="text-3xl font-bold text-gradient-animated">Animated Gradient Text</h3>
            </div>
            <div>
              <h3 className="text-3xl font-bold text-gradient-glow">Glowing Gradient Text</h3>
            </div>
          </div>
        </div>

        {/* Borders Section */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Gradient Borders</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="gradient-border p-6">
              <h3 className="text-lg font-semibold mb-2">Static Gradient Border</h3>
              <p className="text-gray-600">Border với gradient tĩnh đẹp mắt</p>
            </div>
            <div className="gradient-border-animated p-6">
              <h3 className="text-lg font-semibold mb-2">Animated Gradient Border</h3>
              <p className="text-gray-600">Border với gradient động thu hút</p>
            </div>
          </div>
        </div>

        {/* Status Indicators Section */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Status Indicators</h2>
          <div className="flex flex-wrap gap-4">
            <span className="gradient-status-success">Success Status</span>
            <span className="gradient-status-warning">Warning Status</span>
            <span className="gradient-status-error">Error Status</span>
          </div>
        </div>

        {/* Progress Bars Section */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Progress Bars</h2>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Progress 1</span>
                <span className="text-sm text-gray-500">75%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="gradient-progress h-2 rounded-full" style={{width: '75%'}}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Progress 2</span>
                <span className="text-sm text-gray-500">50%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="gradient-progress h-2 rounded-full" style={{width: '50%'}}></div>
              </div>
            </div>
          </div>
        </div>

        {/* Background Effects Section */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Background Effects</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="gradient-bg-subtle p-6 rounded-lg">
              <h3 className="font-semibold mb-2">Subtle Background</h3>
              <p className="text-gray-700">Background gradient nhẹ nhàng</p>
            </div>
            <div className="gradient-bg-light p-6 rounded-lg">
              <h3 className="font-semibold mb-2">Light Background</h3>
              <p className="text-gray-700">Background gradient sáng</p>
            </div>
            <div className="gradient-bg-animated p-6 rounded-lg text-white">
              <h3 className="font-semibold mb-2">Animated Background</h3>
              <p>Background gradient động</p>
            </div>
          </div>
        </div>

        {/* Pagination Section */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Gradient Pagination</h2>
          <Pagination
            currentPage={currentPage}
            totalPages={10}
            onPageChange={setCurrentPage}
            totalItems={100}
            itemsPerPage={10}
          />
        </div>

        {/* Search and Select Section */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Search & Select Components</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-3">Gradient Search Bar</h3>
              <SearchBar
                value={searchValue}
                onChange={setSearchValue}
                placeholder="Tìm kiếm với gradient..."
              />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-3">Gradient Select</h3>
              <GradientSelect
                value={selectValue}
                onChange={setSelectValue}
                options={[
                  { value: 'option1', label: 'Tùy chọn 1' },
                  { value: 'option2', label: 'Tùy chọn 2' },
                  { value: 'option3', label: 'Tùy chọn 3' }
                ]}
                placeholder="Chọn một tùy chọn"
              />
            </div>
          </div>
        </div>

        {/* Menu Bar Section */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Gradient Menu Bar</h2>
          <div className="gradient-menu-bar p-4 rounded-lg">
            <nav className="flex space-x-4">
              <a href="#" className="gradient-menu-item active">Dashboard</a>
              <a href="#" className="gradient-menu-item">Agents</a>
              <a href="#" className="gradient-menu-item">Tickets</a>
              <a href="#" className="gradient-menu-item">Settings</a>
            </nav>
          </div>
        </div>

        {/* Loading Spinner */}
        <div className="bg-white rounded-xl p-6 shadow-lg">
          <h2 className="text-2xl font-semibold text-gradient-primary mb-6">Loading Spinner</h2>
          <div className="flex justify-center">
            <div className="gradient-spinner"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GradientDemo;
