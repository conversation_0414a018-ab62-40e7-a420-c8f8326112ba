variables:
  GIT_STRATEGY: none
  GIT_CHECKOUT: "false"

stages:
  - base-build
  - build
  - code-analysis
  - deploy

base-build-manual:
  stage: base-build
  tags:
    - fti-devenv
  script:
    - trigger -h http://git.fpt.net -u /api/v4/projects -a $CI_BOT_ACCESS_TOKEN -p glptt-53cf96fbb60b6fe844fa1af446e131b17a9cd567 -o 0 -e STAGE="build" -e DOCKER_TAG=$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA -t portal-web 12446
  when: manual

base-build:
  stage: base-build
  tags:
    - fti-devenv
  script:
    - trigger -h http://git.fpt.net -u /api/v4/projects -a $CI_BOT_ACCESS_TOKEN -p glptt-53cf96fbb60b6fe844fa1af446e131b17a9cd567 -o 0 -e STAGE="build" -e DOCKER_TAG=$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA -t portal-web 12446
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"
      changes:
        - package.json


dev-build:
  stage: build
  tags:
    - fti-devenv
  only:
    - dev
  script:
    - trigger -h http://git.fpt.net -u /api/v4/projects -a $CI_BOT_ACCESS_TOKEN -p glptt-f32b703cd0359fe68141093acd7d9920644e79a1 -o 0 -e STAGE="build" -e DOCKER_TAG=$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA -t dev 12916
dev-code-analysis:
  stage: code-analysis
  tags:
    - fti-devenv
  only:
    - dev
  script:
    - trigger -h http://git.fpt.net -u /api/v4/projects -a $CI_BOT_ACCESS_TOKEN -p glptt-f32b703cd0359fe68141093acd7d9920644e79a1 -o 1 -e STAGE="code-analysis" -e DOCKER_TAG=$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA -t dev 12916
dev-deploy:
  stage: deploy
  tags:
    - fti-devenv
  only:
    - dev
  script:
    - trigger -h http://git.fpt.net -u /api/v4/projects -a $CI_BOT_ACCESS_TOKEN -p glptt-f32b703cd0359fe68141093acd7d9920644e79a1 -e STAGE="deploy" -e DOCKER_TAG=$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA -t dev 12916

staging-build:
  stage: build
  tags:
    - fti-devenv
  only:
    - staging
  script:
    - trigger -h http://git.fpt.net -u /api/v4/projects -a $CI_BOT_ACCESS_TOKEN -p glptt-f32b703cd0359fe68141093acd7d9920644e79a1 -o 0 -e STAGE="build" -e DOCKER_TAG=$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA -t staging 12916
staging-deploy:
  stage: deploy
  tags:
    - fti-devenv
  only:
    - staging
  script:
    - trigger -h http://git.fpt.net -u /api/v4/projects -a $CI_BOT_ACCESS_TOKEN -p glptt-f32b703cd0359fe68141093acd7d9920644e79a1 -e STAGE="deploy" -e DOCKER_TAG=$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA -t staging 12916

production-build:
  stage: build
  tags:
    - fti-devenv
  only:
    - tags
  script:
    - trigger -h http://git.fpt.net -u /api/v4/projects -a $CI_BOT_ACCESS_TOKEN -p glptt-f32b703cd0359fe68141093acd7d9920644e79a1 -o 0 -e STAGE="build" -e DOCKER_TAG=$CI_COMMIT_TAG -t main 12916
production-deploy:
  stage: deploy
  tags:
    - fti-devenv
  only:
    - tags
  script:
    - trigger -h http://git.fpt.net -u /api/v4/projects -a $CI_BOT_ACCESS_TOKEN -p glptt-f32b703cd0359fe68141093acd7d9920644e79a1 -e STAGE="deploy" -e DOCKER_TAG=$CI_COMMIT_TAG -t main 12916
