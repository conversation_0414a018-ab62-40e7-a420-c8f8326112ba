
body {
    font-family: '<PERSON>', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-bg);
}

.card {
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: var(--primary-blue);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: var(--primary-blue-hover);
}

.status-active {
    background-color: var(--primary-blue-light);
    color: var(--primary-blue);
}

.status-inactive {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.modal-overlay {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}