import { useEffect, useState } from 'react';
import { Conversation } from '@/types';
import { conversationApi } from '@/api/conversationApi';

export function useConversation(agent?: number) {
  const [conversation, setConversation] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchConversation = async (agent: number) => {
    console.log(agent)
    const conversations = await conversationApi.getConversations(agent)
    if (conversations == null)
      setConversation([])
    else
      setConversation(conversations)
  }

  useEffect(() => {
    if (!agent) return;
    setLoading(true);
    fetchConversation(agent)
    
  }, [agent]);

  return { conversation, loading };
}