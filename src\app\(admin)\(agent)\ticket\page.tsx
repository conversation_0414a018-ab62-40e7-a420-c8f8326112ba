"use client";
import React, { useEffect, useState } from 'react';
import { FaSearch } from 'react-icons/fa';
import clsx from 'clsx';
import { Agent, Ticket, TicketPagination } from '@/types';
import { ticketApi } from '@/api/ticketApi';
import Link from 'next/link';
import { AppRouting } from '@/constants/routes';
import { getPaginationRange } from '@/utils/pagination';
import TicketTableV2 from '@/components/ticket/TicketTableV2';
import PageBreadcrumb from '@/components/common/PageBreadCrumb';

// Đ<PERSON><PERSON> nghĩa kiểu dữ liệu ticket
// Props: agent để gọi API

const statusColors: Record<string, string> = {
  'Chờ xử lý': 'bg-yellow-500',
  'Đã đóng': 'bg-gray-500',
  'Bị từ chối': 'bg-red-500',
  undefined: 'bg-gray-500'
};


const TicketPage: React.FC = () => {
  return (
    <div className="">
      {/* Filters */}
      <PageBreadcrumb pageTitle="Danh sách Ticket" />
      <TicketTableV2 agent={null}/>
    </div>
  );
};

export default TicketPage;
