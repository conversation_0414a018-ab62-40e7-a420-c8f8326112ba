import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "../ui/table";
import { AdditionInfoSetting, AddAdditionInfoSettingRequest, UpdateAdditionInfoSettingRequest } from "@/types";
import Input from "../form/input/InputField";
import Button from "../ui/button/Button";
import { approvalFlowApi } from "@/api/approvalFlowApi";
import { toast } from "react-toastify";
import { Trash2, Edit3, Save, X, Plus, Loader2, ChevronDownIcon } from "lucide-react";
import { Dropdown } from "../ui/dropdown/Dropdown";
import Label from "../form/Label";
import Select from "../form/Select";
import Checkbox from "../form/input/Checkbox";

// Custom controlled input component
const ControlledInput: React.FC<{
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
}> = ({ placeholder, value, onChange, disabled = false, className = "" }) => {
  return (
    <input
      type="text"
      placeholder={placeholder}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      className={`h-[38px] w-full rounded-lg border appearance-none px-4 py-2 text-sm shadow-theme-xs placeholder:text-gray-400 focus:outline-hidden focus:ring-3 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800 ${
        disabled
          ? 'text-gray-500 border-gray-300 cursor-not-allowed dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700'
          : 'bg-transparent text-gray-800 border-gray-300 focus:border-brand-300 focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800'
      } ${className}`}
    />
  );
};

type Props = {
  data: AdditionInfoSetting[];
  stepId: number;
  onDataChange?: (data: AdditionInfoSetting[]) => void;
};

const options = [
    { value: "string", label: "Text" },
    { value: "number", label: "Number" },
    { value: "datetime", label: "DateTime" }
  ];

export default function AdditionalInfoTable({ data, stepId, onDataChange }: Props) {
  const [rows, setRows] = useState<AdditionInfoSetting[]>(data);
  const [newRow, setNewRow] = useState({ fieldName: "", type: "", isRequired: false });
  const [editRowId, setEditRowId] = useState<number | null>(null);
  const [editRowData, setEditRowData] = useState({ fieldName: "", type: "",  isRequired: false});
  const [addingNew, setAddingNew] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [isChecked, setIsChecked] = useState(false);
  const [isCheckedDisabled, setIsCheckedDisabled] = useState(false);
  const [isCheckedEdit, setIsCheckedEdit] = useState(false);

  // Sync with parent data changes
  useEffect(() => {
    setRows(data);
  }, [data]);

  const handleSelectChange = (value: string) => {
    setNewRow({ ...newRow, type: value })
  };

  const handleSelectEditChange = (value: string) => {
    setEditRowData({ ...editRowData, type: value })
  }

  const handleCheckEditChange = (isCheck: boolean) => {
    setIsCheckedEdit(isCheck)
    setEditRowData({ ...editRowData, isRequired: isCheck })
    
  }
  // Notify parent of data changes
  const updateParentData = (newData: AdditionInfoSetting[]) => {
    setRows(newData);
    onDataChange?.(newData);
  };

  // Add row with API call
  const handleAddRow = async () => {
    if (!newRow.fieldName.trim() || !newRow.type.trim()) {
      toast.error("Vui lòng nhập đầy đủ thông tin!");
      return;
    }

    setAddingNew(true);
    try {
      const newItem: AdditionInfoSetting = {
        id: 0, // Will be set by backend
        stepId: stepId,
        fieldName: newRow.fieldName.trim(),
        type: newRow.type.trim(),
        isRequired: isChecked
      };

      const request: AddAdditionInfoSettingRequest = {
        stepId: stepId,
        additionInfos: [newItem]
      };

      const success = await approvalFlowApi.addAdditionInfoSetting(request);

      if (success) {
        // Add to local state with temporary ID
        const tempId = Date.now();
        const newItemWithId = { ...newItem, id: tempId };
        const updatedRows = [...rows, newItemWithId];
        updateParentData(updatedRows);

        // Reset form
        setNewRow({ fieldName: "", type: "", isRequired: false });
        toast.success("Thêm thông tin bổ sung thành công!");
      }
    } catch (error) {
      console.error("Error adding additional info:", error);
      // toast.error("Không thể thêm thông tin bổ sung!");
    } finally {
      setAddingNew(false);
    }
  };

  // Delete row with API call
  const handleDeleteRow = async (id: number) => {
    if (id === 0) {
      // For newly added items that haven't been saved to backend yet
      const updatedRows = rows.filter((r) => r.id !== id);
      updateParentData(updatedRows);
      return;
    }

    setDeletingId(id);
    try {
      const success = await approvalFlowApi.deleteAdditionInfoSetting(id);

      if (success) {
        const updatedRows = rows.filter((r) => r.id !== id);
        updateParentData(updatedRows);
        toast.success("Xóa thông tin bổ sung thành công!");
      }
    } catch (error) {
      console.error("Error deleting additional info:", error);
      // toast.error("Không thể xóa thông tin bổ sung!");
    } finally {
      setDeletingId(null);
    }
  };

  // Start editing
  const handleEdit = (item: AdditionInfoSetting) => {
    setEditRowId(item.id);
    setEditRowData({ fieldName: item.fieldName, type: item.type, isRequired: item.isRequired });
  };

  // Save editing with API call
  const handleSaveEdit = async () => {
    if (!editRowData.fieldName.trim() || !editRowData.type.trim()) {
      toast.error("Vui lòng nhập đầy đủ thông tin!");
      return;
    }

    if (editRowId === null) return;

    setEditingId(editRowId);
    try {
      editRowData.isRequired = isCheckedEdit
      const request: UpdateAdditionInfoSettingRequest = {
        id: editRowId,
        fieldName: editRowData.fieldName.trim(),
        type: editRowData.type.trim(),
        isRequired: editRowData.isRequired
      };

      const success = await approvalFlowApi.updateAdditionInfoSetting(request);

      if (success) {
        const updatedRows = rows.map((r) =>
          r.id === editRowId ? { ...r, ...editRowData } : r
        );
        updateParentData(updatedRows);
        setEditRowId(null);
        toast.success("Cập nhật thông tin bổ sung thành công!");
      }
    } catch (error) {
      console.error("Error updating additional info:", error);
      // toast.error("Không thể cập nhật thông tin bổ sung!");
    } finally {
      setEditingId(null);
    }
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditRowId(null);
    setEditRowData({ fieldName: "", type: "", isRequired: false });
  };

  return (
    <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03] p-6">
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
          Thông tin bổ sung
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Quản lý các thông tin bổ sung cần thiết cho bước phê duyệt này
        </p>
      </div>

      {/* Add new row form */}
      <div className="mb-6 p-4 bg-gray-50 dark:bg-white/[0.02] rounded-lg border border-gray-100 dark:border-white/[0.05]">
        <div className="md:flex justify-between gap-3 items-end">
          <div className="md:flex md:flex-1 gap-3">
          <div className="flex-1">
            <Label>Tên thông tin</Label>
            <ControlledInput
              placeholder="Nhập tên thông tin..."
              value={newRow.fieldName}
              onChange={(value) => setNewRow({ ...newRow, fieldName: value })}
              disabled={addingNew}
            />
          </div>
          <div className="flex-1">
            
            <Label>Loại dữ liệu</Label>
            <div className="relative">
              <Select
                options={options}
                placeholder="Select Option"
                onChange={handleSelectChange}
                className="dark:bg-dark-900"
              />
              <span className="absolute text-gray-500 -translate-y-1/2 pointer-events-none right-3 top-1/2 dark:text-gray-400">
                  <ChevronDownIcon/>
              </span>
            </div>
          </div>

          <div className="flex-1">
            <Label>Bắt buộc</Label>
            <div className="flex h-[38px]">
              <Checkbox checked={isChecked} onChange={setIsChecked} />
            </div>
          </div>
          </div>
          <Button
            onClick={handleAddRow}
            disabled={addingNew || !newRow.fieldName.trim() || !newRow.type.trim()}
            className="h-[38px] px-4 flex items-center gap-2"
          >
            {addingNew ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Đang thêm...
              </>
            ) : (
              <>
                <Plus className="w-4 h-4" />
                Thêm
              </>
            )}
          </Button>
          
        </div>
        
      </div>

      {/* Table */}
      <div className="max-w-full overflow-x-auto max-h-[60vh] overflow-y-auto border border-gray-200 dark:border-white/[0.05] rounded-lg">
        {rows.length === 0 ? (
          <div className="p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-white/[0.05] rounded-full flex items-center justify-center">
              <Plus className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Chưa có thông tin bổ sung
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Thêm thông tin bổ sung đầu tiên bằng form ở trên
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader className="border-b border-gray-100 dark:border-white/[0.05] bg-gray-50 dark:bg-white/[0.02]">
              <TableRow>
                <TableCell isHeader className="px-6 py-4 text-gray-700 font-semibold text-start dark:text-gray-300">
                  Tên thông tin
                </TableCell>
                <TableCell isHeader className="px-6 py-4 text-gray-700 font-semibold text-start dark:text-gray-300">
                  Loại dữ liệu
                </TableCell>
                <TableCell isHeader className="px-6 py-4 text-gray-700 font-semibold text-start dark:text-gray-300">
                  Bắt buộc
                </TableCell>
                <TableCell isHeader className="px-6 py-4 text-gray-700 font-semibold text-center dark:text-gray-300">
                  Hành động
                </TableCell>
              </TableRow>
            </TableHeader>

            <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
              {rows.map((item) => (
                <TableRow key={item.id} className="hover:bg-gray-50 dark:hover:bg-white/[0.02] transition-colors">
                  <TableCell className="px-6 py-4 text-start">
                    {editRowId === item.id ? (
                      <Input
                        defaultValue={editRowData.fieldName}
                        onChange={(e) => setEditRowData({ ...editRowData, fieldName: e.target.value })}
                        disabled={editingId === item.id}
                        className="min-w-[200px]"
                      />
                    ) : (
                      <span className="font-medium text-gray-900 dark:text-white">
                        {item.fieldName}
                      </span>
                    )}
                  </TableCell>
                  <TableCell className="px-6 py-4 text-start">
                    {editRowId === item.id ? (
                      <div className="flex-1">
            
                      <div className="relative">
                        <Select
                          options={options}
                          defaultValue={item.type}
                          placeholder="Select Option"
                          onChange={handleSelectEditChange}
                          className="dark:bg-dark-900"
                        />
                        <span className="absolute text-gray-500 -translate-y-1/2 pointer-events-none right-3 top-1/2 dark:text-gray-400">
                            <ChevronDownIcon/>
                        </span>
                      </div>
                    </div>
                    ) : (
                      <span className="text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-white/[0.05] px-3 py-1 rounded-full text-sm">
                        {item.type}
                      </span>
                    )}
                  </TableCell>
                  <TableCell className="px-6 py-4 text-start">
                    {editRowId === item.id ? (
                      <div className="flex-1">
            
                      <div className="relative">
                        <Checkbox checked={isCheckedEdit} onChange={handleCheckEditChange} />
                      </div>
                    </div>
                    ) : (
                      <Checkbox checked={item.isRequired??false} disabled onChange={setIsCheckedDisabled} />
                    )}
                  </TableCell>
                  <TableCell className="px-6 py-4 text-center">
                    <div className="flex justify-center gap-2">
                      {editRowId === item.id ? (
                        <>
                          <Button
                            size="sm"
                            onClick={handleSaveEdit}
                            disabled={editingId === item.id}
                            className="h-8 px-3 flex items-center gap-1"
                          >
                            {editingId === item.id ? (
                              <>
                                <Loader2 className="w-3 h-3 animate-spin" />
                                Đang lưu...
                              </>
                            ) : (
                              <>
                                <Save className="w-3 h-3" />
                                Lưu
                              </>
                            )}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={handleCancelEdit}
                            disabled={editingId === item.id}
                            className="h-8 px-3 flex items-center gap-1"
                          >
                            <X className="w-3 h-3" />
                            Hủy
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(item)}
                            disabled={deletingId === item.id}
                            className="h-8 px-3 flex items-center gap-1 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600"
                          >
                            <Edit3 className="w-3 h-3" />
                            Sửa
                          </Button>
                          <Button
                            size="sm"
                            variant="primary"
                            onClick={() => handleDeleteRow(item.id)}
                            disabled={deletingId === item.id}
                            className="h-8 px-3 flex items-center gap-1 bg-red-500 hover:bg-red-600 border-red-500 hover:border-red-600"
                          >
                            {deletingId === item.id ? (
                              <>
                                <Loader2 className="w-3 h-3 animate-spin" />
                                Đang xóa...
                              </>
                            ) : (
                              <>
                                <Trash2 className="w-3 h-3" />
                                Xóa
                              </>
                            )}
                          </Button>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>
    </div>
  );
}
