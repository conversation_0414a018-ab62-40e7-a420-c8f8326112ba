import React, { ReactNode } from "react";

interface ButtonProps {
  children: ReactNode; // Button text or content
  size?: "sm" | "md"; // Button size
  variant?: "primary" | "outline"; // Button variant
  startIcon?: ReactNode; // Icon before the text
  endIcon?: ReactNode; // Icon after the text
  onClick?: () => void; // Click handler
  disabled?: boolean; // Disabled state
  className?: string; // Disabled state
}

const Button: React.FC<ButtonProps> = ({
  children,
  size = "md",
  variant = "primary",
  startIcon,
  endIcon,
  onClick,
  className = "",
  disabled = false,
}) => {
  // Size Classes - Updated to achieve 38px height
  const sizeClasses = {
    sm: "px-4 py-2 text-sm h-[38px]",
    md: "px-5 py-2.5 text-sm h-[40px]",
  };

  // Variant Classes
  const variantClasses = {
    primary:
      "bg-gradient-primary text-white shadow-theme-xs hover:bg-gradient-primary-hover transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed",
    outline:
      "border border-blue-700 bg-white text-gradient-primary ring-1 ring-inset ring-gray-300 dark:bg-gray-800 hover:opacity-50 dark:ring-gray-700 dark:hover:bg-gradient-primary dark:hover:text-gray-300 dark:text-blue-200 dark:border-blue-300",
  };

  return (
    <button
      className={`inline-flex items-center justify-center font-medium gap-2 rounded-lg transition  ${className} ${
        sizeClasses[size]
      } ${variantClasses[variant]} ${
        disabled ? "cursor-not-allowed opacity-50" : ""
      }`}
      onClick={onClick}
      disabled={disabled}
    >
      {startIcon && <span className="flex items-center">{startIcon}</span>}
      {children}
      {endIcon && <span className="flex items-center">{endIcon}</span>}
    </button>
  );
};

export default Button;
