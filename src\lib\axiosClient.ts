import { redirectToKeycloakLogin } from '@/utils/auth';
import { toast } from 'react-toastify';

import axios from 'axios';

const isClient = typeof window !== 'undefined';

const axiosClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

let isShowingError = false;

axiosClient.interceptors.request.use(
   (config) => {
        const token = localStorage.getItem('token'); 
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
            config.headers['GenAIAuthorization'] = `Bearer ${token}`;
        }

        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
)

axiosClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (!error.response && isClient) {
      if (!isShowingError)
      {
        isShowingError = true;
        console.error('Lỗi mạng: <PERSON><PERSON><PERSON> kiểm tra kết nối của bạn');
        toast.error(`Lỗi mạng: <PERSON><PERSON><PERSON> kiểm tra kết nối của bạn`);
      }
      
    } else if (isClient){
        switch (error.response.status) {
            case 401:
              redirectToKeycloakLogin()
              break;
            case 500:
              if (isShowingError)
                break;
              isShowingError = true;
              console.error('Lỗi hệ thống. Vui lòng liên hệ Quản trị hệ thống để được hỗ trợ');
              toast.error(`Lỗi hệ thống. Vui lòng liên hệ Quản trị hệ thống để được hỗ trợ`);
              break;
            
            default:
              if (isShowingError)
                break;
              isShowingError = true;
              console.log(`${error.response.data}`);
              const message =
                  error.response.data.message ||
                  'Lỗi không xác định';
              toast.error(`${message}`);
        }
      setTimeout(() => {
        isShowingError = false;
      }, 3000);
    }

    const customMessage = error.response?.data?.message || 'Something went wrong';
    return Promise.reject(new Error(customMessage));
  }
);

export default axiosClient;