
.card {
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: var(--primary-blue);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: var(--primary-blue-hover);
}

.status-active {
    background-color: var(--primary-blue-light);
    color: var(--primary-blue);
}

.status-inactive {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.modal-overlay {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

// Line clamp utility
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

// View mode transitions
.view-transition {
    transition: all 0.3s ease-in-out;
}

// Table hover effects
.table-row-hover:hover {
    background-color: rgba(55, 82, 216, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

// List view animations
.list-item {
    transition: all 0.3s ease;
}

.list-item:hover {
    transform: translateX(4px);
    box-shadow: 0 8px 16px rgba(55, 82, 216, 0.15);
}

// Compact view grid
.compact-grid {
    transition: all 0.3s ease;
}

.compact-grid:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 12px rgba(55, 82, 216, 0.2);
}

// View mode selector
.view-selector {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 4px;
}

.view-selector-btn {
    transition: all 0.2s ease;
    border-radius: 8px;
    padding: 8px 12px;
}

.view-selector-btn.active {
    background: white;
    color: #3752D8;
    box-shadow: 0 2px 4px rgba(55, 82, 216, 0.2);
}

.view-selector-btn:hover:not(.active) {
    background: rgba(55, 82, 216, 0.1);
    color: #3752D8;
}