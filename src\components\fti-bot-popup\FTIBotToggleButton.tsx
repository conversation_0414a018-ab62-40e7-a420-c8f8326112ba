"use client";

import React from "react";
import { useFTIBotPopup } from "@/context/FTIBotPopupContext";
import { FaRobot, FaComments } from "react-icons/fa";

export default function FTIBotToggleButton() {
  const { isOpen, togglePopup } = useFTIBotPopup();

  return (
    <button
      onClick={togglePopup}
      className={`fixed bottom-6 right-6 w-14 h-14 rounded-full shadow-2xl flex items-center justify-center transition-all duration-300 ease-in-out z-[9998] hover:scale-110 ${
        isOpen 
          ? 'bg-gray-500 hover:bg-gray-600' 
          : 'bg-gradient-primary hover:bg-gradient-primary-hover'
      }`}
      title={isOpen ? "Đóng FTI Bot" : "Mở FTI Bot"}
    >
      {isOpen ? (
        <FaComments className="text-white text-xl" />
      ) : (
        <FaRobot className="text-white text-xl animate-pulse" />
      )}
      
      {/* Notification dot when closed */}
      {/* {!isOpen && (
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
          <div className="w-2 h-2 bg-white rounded-full animate-ping"></div>
        </div>
      )} */}
    </button>
  );
}
