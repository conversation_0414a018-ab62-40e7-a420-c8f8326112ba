'use client';
import { useState, useRef, useEffect } from 'react';
import { FaPaperPlane, FaPaperclip, FaComments, FaTimes } from 'react-icons/fa';
import Image from 'next/image';
import { ChatMessage } from '@/types';
import { conversationApi } from '@/api/conversationApi';
import MarkdownViewer from '../markdownviewer/MarkdownViewer';
import styles from './AgentChat.module.scss';

interface ChatWidgetUIProps
{
    ticketId: number
}

const ChatWidgetUI: React.FC<ChatWidgetUIProps> = ({ticketId}) => {
  const [open, setOpen] = useState(false);
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [fileNames, setFileNames] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const chatRef = useRef<HTMLDivElement>(null);
  const [conversationId, setConversationId] = useState(0)

  const agentCode = process.env.NEXT_PUBLIC_AGENT_CODE_TICKET || "TICKETSUPPORT";
  

  useEffect(() => {
    chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight, behavior: 'smooth' });
  }, [messages]);

  const handleSend = async () => {
    if (!input.trim()) return;
    if (input.length > 0) {
        const inputValue = input

        setMessages(prev => [...prev, {
            "query": input,
            "answer": "(thinking)",
            "conversationId": conversationId,
            "createTicket": false,
            "id": ""
        }])
        setInput("")

        try {
            if(!agentCode)
            {
                return
            }
            const input: Record<string, string> = {
                ticketId: ""+ticketId,
            };

            const data = await conversationApi.sendMessage(inputValue, conversationId, [], agentCode, input)
            if (data != null) {
                if(data.conversationId)
                {
                  setConversationId(data.conversationId)
                }
                setMessages(prevMessages => {
                    const lastIndex = prevMessages.length - 1;

                    if (prevMessages[lastIndex]?.answer === '(thinking)') {
                        const updatedMessages = [...prevMessages];
                        updatedMessages[lastIndex] = data;
                        return updatedMessages;
                    }
                    return prevMessages;
                });
            }
        } catch (err) {

        }
    }
  };

  const handleUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;
    const names = Array.from(files).map(f => f.name);
    setFileNames(prev => [...prev, ...names]);
  };

  const removeFile = (name: string) => {
    setFileNames(prev => prev.filter(f => f !== name));
  };

  return (
    <>
      {/* Nút mở chat */}
      <div
        className="fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg cursor-pointer z-50"
        onClick={() => setOpen(!open)}
      >
        {open ? <FaTimes /> : <FaComments />}
      </div>

      {/* Khung chat */}
      {open && (
        <div className="fixed bottom-24 right-6 w-[500px] h-[600px] bg-white border border-gray-300 rounded-lg shadow-xl flex flex-col z-50">
          {/* Header */}
          <div className="flex items-center p-3 border-b text-sm">
            <div className="w-8 h-8 rounded-full bg-blue-100 mr-2 relative overflow-hidden">
              <Image src="/agent-ai/images/agent/ai-ui.png" alt="AI" fill />
            </div>
            <div>
              <div className="font-semibold text-gray-900">Trợ lý AI</div>
              <div className="text-xs text-gray-500 flex items-center gap-x-2"><span className="w-2 h-2 rounded-full bg-green-500 over"></span>Đang trực tuyến</div>
            </div>
          </div>

          {/* Nội dung chat */}
          <div className="flex-1 overflow-y-auto p-3 space-y-3 text-sm" ref={chatRef}>
            {<div className={`${styles.chat_message_user} flex`}>
                {[...Array(5)].map((_, i) => (
                    <div
                        key={i}
                        className="w-1 bg-blue-500 rounded"
                        style={{
                            animation: `equalizer 1s ease-in-out infinite`,
                            animationDelay: `${i * 0.1}s`,
                            height: "100%",
                        }}
                    />
                ))}</div>}
            {
                messages.map((value: any, index: number) => {
                    const query = value.query
                    const answer = value.answer
                    return <div key={index} className='space-y-4'>
                        <div className="flex justify-end">
                            <div className={`${styles.chat_message_user} px-4 py-3 max-w-xs lg:max-w-md`}>
                                <p className="text-white">{query}</p>
                                {/* <p className="text-xs text-gray-500 mt-2">Bạn • {formatRelativeTime(value.created_at)}</p> */}
                            </div>
                        </div>
                        {
                            (answer != '(thinking)' && answer != null && answer.length > 0) ? <div className="flex">
                                <div className="flex-shrink-0 mr-3">
                                    <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center relative overflow-hidden">
                                        <Image
                                            src="/agent-ai/images/agent/ai-ui.png"
                                            alt="ai"
                                            className='w-full'
                                            fill={true}
                                        />
                                    </div>
                                </div>
                                <div className={`${styles.chat_message_ai} px-4 py-3 max-w-xs lg:max-w-md`}>
                                    {/* <p className="text-gray-800">{answer}</p> */}
                                    <MarkdownViewer content={answer} />
                                    {/* <p className="text-xs text-gray-500 mt-2">AI Agent • Just now</p> */}
                                </div>
                            </div> : (answer != null && answer.length > 0) ? <div className="flex gap-1 items-center h-6">
                                {[...Array(5)].map((_, i) => (
                                    <div
                                        key={i}
                                        className="w-1 bg-blue-500 rounded"
                                        style={{
                                            animation: `equalizer 1s ease-in-out infinite`,
                                            animationDelay: `${i * 0.1}s`,
                                            height: "100%",
                                        }}
                                    />
                                ))}
                                <div className='opacity-50 text-sm ml-2'>Đang suy nghĩ...</div>
                            </div> : ""
                        }

                    </div>
                })
            }
          </div>

          {/* Gửi tin nhắn */}
          <div className="p-3 border-t space-y-2">
            {/* Hiển thị file upload */}
            {fileNames.length > 0 && (
              <div className="flex flex-wrap gap-2 max-h-16 overflow-y-auto">
                {fileNames.map((f, i) => (
                  <div
                    key={i}
                    className="bg-gray-200 px-3 py-1 rounded-2xl text-xs flex items-center justify-between gap-2"
                  >
                    <span>{f}</span>
                    <button onClick={() => removeFile(f)} className="text-gray-600 font-bold ml-1">×</button>
                  </div>
                ))}
              </div>
            )}

            <div className="flex items-center gap-2">
              {/* <button onClick={() => fileInputRef.current?.click()} className="text-gray-500 hover:text-blue-600">
                <FaPaperclip />
              </button> */}
              <input type="file" className="hidden" ref={fileInputRef} onChange={handleUpload} multiple />
              <input
                type="text"
                className="flex-1 border border-gray-300 rounded-lg px-3 py-1 focus:outline-none text-sm"
                placeholder="Nhập nội dung..."
                value={input}
                onChange={e => setInput(e.target.value)}
                onKeyDown={e => e.key === 'Enter' && handleSend()}
              />
              <button
                onClick={handleSend}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-lg text-sm flex items-center"
              >
                Gửi <FaPaperPlane className="ml-1" />
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ChatWidgetUI;
