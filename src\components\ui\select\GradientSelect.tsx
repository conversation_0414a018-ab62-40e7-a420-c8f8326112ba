'use client';

import React from 'react';

interface Option {
  value: string;
  label: string;
}

interface GradientSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const GradientSelect: React.FC<GradientSelectProps> = ({
  value,
  onChange,
  options,
  placeholder = "Chọn một tùy chọn",
  className = "",
  disabled = false
}) => {
  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      className={`px-4 py-2 h-[38px] border-2 border-gray-200 rounded-lg bg-white shadow-sm
        focus:border-transparent focus:outline-none focus:ring-2 focus:ring-brand-500
        transition-all duration-300 hover:border-gray-300
        disabled:opacity-50 disabled:cursor-not-allowed
        ${className}`}
    >
      {placeholder && (
        <option value="" disabled>
          {placeholder}
        </option>
      )}
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
};

export default GradientSelect;
