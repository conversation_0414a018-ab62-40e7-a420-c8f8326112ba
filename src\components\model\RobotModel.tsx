// components/RobotModel.tsx
'use client'

import { useGLTF, useAnimations, OrbitControls } from '@react-three/drei'
import { Canvas } from '@react-three/fiber'
import { useRef, useEffect, useState } from 'react'
import { Group } from 'three'
import * as THREE from 'three'

function Robot() {
  const group = useRef<Group>(null)
  const { scene, animations } = useGLTF('/models/human_wave.glb')
  const { actions } = useAnimations(animations, group)
  const [adjustedScene, setAdjustedScene] = useState<THREE.Object3D | null>(null)

  useEffect(() => {
    console.log("Available animations:", Object.keys(actions))
    if (actions && actions["wave-hand-and-s"]) {
        const action = actions["wave-hand-and-s"]
        action.reset()                        // Đảm bảo animation bắt đầu từ đầu
        action.setLoop(THREE.LoopOnce, 1)     // Chạy 1 lần
        action.clampWhenFinished = true       // Giữ nguyên frame cuối cùng
        action.play()
    }
  }, [actions])
  
  return (
    <group ref={group}>
      <primitive object={scene} scale={1.5} />
    </group>
  )
}

export default function RobotModelCanvas() {
  return (
    <Canvas className="w-full h-full" camera={{ position: [0, 2, 5], fov: 50 }}>
      <ambientLight />
      <directionalLight position={[0, 5, 5]} />
      <Robot />
      <OrbitControls />
    </Canvas>
  )
}
