"use client";

// import { Inter } from 'next/font/google';
import { AuthProvider } from '@/context/AuthContext';
import './globals.css';
import localFont from 'next/font/local';

const inter = localFont({
  src: [
    {
      path: './fonts/Inter-VariableFont_opsz,wght.ttf',
      style: 'normal',
      weight: '100 900'
    },
    {
      path: './fonts/Inter-Italic-VariableFont_opsz,wght.ttf',
      style: 'italic',
      weight: '100 900'
    }
  ],
  variable: '--font-inter',
  display: 'swap',
});
// const outfit = Inter({
//   subsets: ['latin'],
//   display: 'swap',
// });

import { SidebarProvider } from '@/context/SidebarContext';
import { ThemeProvider } from '@/context/ThemeContext';
import { ToastContainer } from 'react-toastify';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        <title>FTI Agent AI</title>
      </head>
      <body>
        
      <AuthProvider>
        <ThemeProvider>
          <SidebarProvider>
              {children}
          </SidebarProvider>
        </ThemeProvider>
      </AuthProvider>
      <ToastContainer
        position="bottom-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        pauseOnHover
        draggable
        theme="light"
        style={{ zIndex: 1000000 }}
      />
      </body>
    </html>
  );
}
