'use client';

import React from 'react';
import clsx from 'clsx';
import { getPaginationRange } from '@/utils/pagination';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showInfo?: boolean;
  totalItems?: number;
  itemsPerPage?: number;
  className?: string;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  showInfo = true,
  totalItems = 0,
  itemsPerPage = 10,
  className = ''
}) => {
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <div className={clsx('flex justify-between items-center', className)}>
      {/* {showInfo && (
        <p className="text-sm text-gray-600">
          Hi<PERSON>n thị từ {startItem} đến {endItem} trên tổng số {totalItems} kết quả
        </p>
      )} */}
      
      <div className="gradient-pagination">
        <button
          onClick={() => onPageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className="gradient-pagination-button dark:bg-gray-700 dark:text-gray-50"
        >
          Trước
        </button>
        
        {getPaginationRange(currentPage, totalPages).map((page, idx) =>
          page === '...' ? (
            <span key={currentPage * totalPages + idx} className="gradient-pagination-ellipsis">...</span>
          ) : (
            <button
              key={currentPage * totalPages + idx}
              onClick={() => onPageChange(page as number)}
              className={clsx(
                'gradient-pagination-button',
                page === currentPage ? 'active' : ''
              )}
            >
              {page}
            </button>
          )
        )}
        
        <button
          onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage >= totalPages}
          className="gradient-pagination-button dark:bg-gray-700 dark:text-gray-50"
        >
          Sau
        </button>
      </div>
    </div>
  );
};

export default Pagination;
