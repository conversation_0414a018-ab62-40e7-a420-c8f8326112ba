import { useEffect, useRef } from 'react';
import { FaMicrophone, FaMicrophoneAlt } from 'react-icons/fa';

interface VoiceInputProps {
  onTranscript: (transcript: string) => void;
  enabled?: boolean;
}

const VoiceInput = ({onTranscript, enabled = true}: VoiceInputProps) => {
  const recognitionRef = useRef<any>(null);

  const handleClick = () => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

    if (SpeechRecognition) {
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.lang = 'vi-VN';

      recognitionRef.current.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        onTranscript(transcript);
      };

      recognitionRef.current.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
      };

      recognitionRef.current.start();
    } else {
      alert('Tr<PERSON><PERSON> duyệt của bạn không hỗ trợ speech recognition!');
    }
  };

  return (
    <div>
      <button onClick={handleClick} disabled={!enabled}><FaMicrophoneAlt className='text-gray-500 mt-0.5' fontSize={18}/></button>
    </div>
  );
};

export default VoiceInput;
