import axiosClient from '@/lib/axiosClient';
import { Convers<PERSON>, ChatMessage, Agent, ParamItem, CreateFlowRequest, ApprovalFlow, UpdateFlowRequest, AddAdditionInfoSettingRequest, UpdateAdditionInfoSettingRequest } from '@/types/index';

export const approvalFlowApi = {
    async addFlow(data: CreateFlowRequest, signal?: AbortSignal): Promise<boolean> {
        const res = await axiosClient.post('/v1/approval-flow/add-approval-flow', JSON.stringify(data), {signal});
        return res.data.data;
    },
    async getFlowDetail(moduleId: number, signal?: AbortSignal): Promise<ApprovalFlow | null> {
        const res = await axiosClient.get('/v1/approval-flow/get-detail-approval-flow-by-module', { params : { moduleId: moduleId }, signal });
        return res.data.data;
    },
    async updateFlow(data: UpdateFlowRequest, signal?: AbortSignal): Promise<boolean | null> {
        const res = await axiosClient.post('/v1/approval-flow/update-approval-flow', JSON.stringify(data), {signal});
        return res.data.data;
    },
    async addAdditionInfoSetting(data: AddAdditionInfoSettingRequest, signal?: AbortSignal): Promise<boolean> {
        const res = await axiosClient.post('/v1/approval-flow/add-addition-info-setting', JSON.stringify(data), {signal});
        return res.data.data;
    },
    async updateAdditionInfoSetting(data: UpdateAdditionInfoSettingRequest, signal?: AbortSignal): Promise<boolean> {
        const res = await axiosClient.post('/v1/approval-flow/update-addition-info-setting', JSON.stringify(data), {signal});
        return res.data.data;
    },
    async deleteAdditionInfoSetting(id: number, signal?: AbortSignal): Promise<boolean> {
        const res = await axiosClient.post('/v1/approval-flow/delete-addition-info-setting', null, {params: {id: id } ,signal});
        return res.data.data;
    }
}